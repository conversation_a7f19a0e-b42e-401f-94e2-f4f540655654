// Supabase Edge Function for bidirectional SCORM conversion
// This function provides conversion between learning path format and SCORM-compliant format

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"

// Import types and conversion functions
import {
  LearningPathFormat,
  SCORMFormat,
  ConversionRequest,
  ConversionResponse,
  ConversionOptions
} from './types.ts'

import { convertToSCORM } from './to-scorm.ts'
import { convertFromSCORM } from './from-scorm.ts'

import {
  validateConversionRequest,
  validateConversionOptions,
  validateAndSanitizeInput,
  createErrorResponse,
  createSuccessResponse,
  logValidationResult
} from './validation.ts'

import { logConversion } from './utils.ts'

console.log("SCORM Converter Edge Function loaded")

serve(async (req) => {
  // CORS headers for all responses
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
    'Access-Control-Allow-Methods': 'POST, OPTIONS',
  }

  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  // Only allow POST requests
  if (req.method !== 'POST') {
    return new Response(
      JSON.stringify({ 
        success: false, 
        error: 'Method not allowed. Only POST requests are supported.' 
      }),
      { 
        status: 405, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }

  try {
    // Parse request body
    let body: any;
    try {
      body = await req.json();
      logConversion('Request received', {
        type: body?.type,
        hasData: !!body?.data,
        hasOptions: !!body?.options
      });
    } catch (parseError) {
      logConversion('JSON parse error', { error: parseError.message });
      return createErrorResponse('Invalid JSON in request body', 400);
    }

    // Validate request structure
    const requestValidation = validateConversionRequest(body);
    logValidationResult('Request validation', requestValidation, { bodyKeys: Object.keys(body || {}) });

    if (!requestValidation.isValid) {
      return createErrorResponse(
        `Request validation failed: ${requestValidation.errors.join(', ')}`,
        400,
        { errors: requestValidation.errors, warnings: requestValidation.warnings }
      );
    }

    // Validate options if provided
    const optionsValidation = validateConversionOptions(body.options);
    logValidationResult('Options validation', optionsValidation);

    if (!optionsValidation.isValid) {
      return createErrorResponse(
        `Options validation failed: ${optionsValidation.errors.join(', ')}`,
        400,
        { errors: optionsValidation.errors, warnings: optionsValidation.warnings }
      );
    }

    // Validate and sanitize input data
    const inputValidation = validateAndSanitizeInput(body.type, body.data);
    logValidationResult('Input data validation', {
      isValid: inputValidation.isValid,
      errors: inputValidation.errors,
      warnings: inputValidation.warnings
    });

    if (!inputValidation.isValid) {
      return createErrorResponse(
        `Input data validation failed: ${inputValidation.errors.join(', ')}`,
        400,
        {
          errors: inputValidation.errors,
          warnings: inputValidation.warnings,
          conversionType: body.type
        }
      );
    }

    // Perform conversion
    const options: ConversionOptions = body.options || {};
    let convertedData: any;
    let resultFormat: 'scorm' | 'learning_path';

    if (body.type === 'to-scorm') {
      logConversion('Starting learning path to SCORM conversion');
      convertedData = convertToSCORM(inputValidation.sanitizedData as LearningPathFormat, options);
      resultFormat = 'scorm';
    } else {
      logConversion('Starting SCORM to learning path conversion');
      convertedData = convertFromSCORM(inputValidation.sanitizedData as SCORMFormat, options);
      resultFormat = 'learning_path';
    }

    // Combine all warnings
    const allWarnings = [
      ...requestValidation.warnings,
      ...optionsValidation.warnings,
      ...inputValidation.warnings
    ];

    logConversion('Conversion completed successfully', {
      resultFormat,
      warningCount: allWarnings.length
    });

    return createSuccessResponse(convertedData, resultFormat, allWarnings);

  } catch (error) {
    logConversion('Conversion failed with error', {
      error: error.message,
      stack: error.stack
    });

    return createErrorResponse(error, 500);
  }
})
