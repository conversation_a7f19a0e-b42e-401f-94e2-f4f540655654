# SCORM Converter Edge Function

A Supabase Edge Function that provides bidirectional conversion between learning path format and SCORM-compliant format.

## Overview

This Edge Function handles conversion between two formats:
- **Learning Path Format**: The current format used by the Learnify Flow Studio application
- **SCORM Format**: Industry-standard format for e-learning content packages

## Features

- ✅ **Bidirectional Conversion**: Convert from learning path to SCORM and vice versa
- ✅ **SCORM 1.2 & 2004 Support**: Compatible with both major SCORM versions
- ✅ **YouTube Video Integration**: Handles YouTube links with proper embedding
- ✅ **Content Type Support**: Videos, text, quizzes, and interactive content
- ✅ **Comprehensive Validation**: Input validation and error handling
- ✅ **Manifest Generation**: Proper SCORM manifest XML generation
- ✅ **Progress Tracking**: SCORM API integration for completion tracking

## API Endpoints

### POST `/functions/v1/scorm-converter`

Converts between learning path and SCORM formats.

#### Request Format

```json
{
  "type": "to-scorm" | "from-scorm",
  "data": {
    // Learning path or SCORM data structure
  },
  "options": {
    "scormVersion": "1.2" | "2004",
    "includeVideos": boolean,
    "packageTitle": string,
    "packageDescription": string,
    "language": string,
    "author": string,
    "generateUniqueIds": boolean,
    "preserveStructure": boolean
  }
}
```

#### Response Format

**Success Response:**
```json
{
  "success": true,
  "data": {
    // Converted data structure
  },
  "format": "scorm" | "learning_path",
  "warnings": ["warning messages"],
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

**Error Response:**
```json
{
  "success": false,
  "error": "Error message",
  "code": "ERROR_CODE",
  "details": {
    // Additional error context
  },
  "suggestions": ["suggestion messages"],
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

## Data Formats

### Learning Path Format

```json
{
  "title": "Course Title",
  "img_url": "https://example.com/course-image.jpg",
  "journey": {
    "Module Name": [
      {
        "title": "Lesson Title",
        "link": "https://www.youtube.com/watch?v=VIDEO_ID",
        "type": "video",
        "duration": "10:33",
        "img_url": "https://example.com/thumbnail.jpg"
      }
    ]
  }
}
```

### SCORM Format

```json
{
  "manifest": {
    "identifier": "course_id",
    "version": "1.0",
    "metadata": {
      "schema": "ADL SCORM",
      "schemaversion": "2004 4th Edition",
      "title": "Course Title",
      "description": "Course Description",
      "language": "en-US",
      "author": "Author Name"
    },
    "organizations": {
      "default": "org_id",
      "organizations": [
        {
          "identifier": "org_id",
          "title": "Course Title",
          "items": [
            {
              "identifier": "item_id",
              "identifierref": "resource_id",
              "title": "Item Title"
            }
          ]
        }
      ]
    },
    "resources": [
      {
        "identifier": "resource_id",
        "type": "webcontent",
        "href": "index.html",
        "scormType": "sco",
        "files": ["index.html", "styles.css"]
      }
    ]
  },
  "contentFiles": {
    "index.html": "<!DOCTYPE html>...",
    "styles.css": "/* CSS content */",
    "imsmanifest.xml": "<?xml version=\"1.0\"?>..."
  }
}
```

## Usage Examples

### Convert Learning Path to SCORM

```javascript
const response = await fetch('https://your-project.supabase.co/functions/v1/scorm-converter', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer YOUR_ANON_KEY'
  },
  body: JSON.stringify({
    type: 'to-scorm',
    data: {
      title: 'JavaScript Fundamentals',
      img_url: 'https://example.com/js-course.jpg',
      journey: {
        'Introduction': [
          {
            title: 'What is JavaScript?',
            link: 'https://www.youtube.com/watch?v=W6NZfCO5SIk',
            type: 'video',
            duration: '15:30'
          }
        ],
        'Variables and Data Types': [
          {
            title: 'Understanding Variables',
            link: 'https://www.youtube.com/watch?v=9WIJQDvt4Us',
            type: 'video',
            duration: '12:45'
          }
        ]
      }
    },
    options: {
      scormVersion: '2004',
      includeVideos: true,
      packageTitle: 'JavaScript Fundamentals SCORM Package',
      author: 'Your Name'
    }
  })
});

const result = await response.json();
if (result.success) {
  console.log('SCORM package generated:', result.data);
} else {
  console.error('Conversion failed:', result.error);
}
```

### Convert SCORM to Learning Path

```javascript
const response = await fetch('https://your-project.supabase.co/functions/v1/scorm-converter', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer YOUR_ANON_KEY'
  },
  body: JSON.stringify({
    type: 'from-scorm',
    data: {
      manifest: {
        // SCORM manifest structure
      },
      contentFiles: {
        // SCORM content files
      }
    },
    options: {
      preserveStructure: true
    }
  })
});

const result = await response.json();
if (result.success) {
  console.log('Learning path generated:', result.data);
} else {
  console.error('Conversion failed:', result.error);
}
```

## Error Codes

| Code | Description |
|------|-------------|
| `CONVERSION_FAILED` | General conversion failure |
| `INVALID_MANIFEST` | SCORM manifest validation failed |
| `INVALID_INPUT` | Input data validation failed |
| `MISSING_REQUIRED_FIELD` | Required field is missing |
| `UNSUPPORTED_FORMAT` | Unsupported data format |
| `YOUTUBE_PARSE_ERROR` | Failed to parse YouTube URL |
| `DURATION_PARSE_ERROR` | Failed to parse duration format |

## Content Type Support

### Videos
- YouTube URLs (watch and embed formats)
- Direct video file URLs
- Embedded video players with SCORM tracking

### Text Content
- HTML content blocks
- Markdown support (converted to HTML)
- Rich text formatting

### Quizzes
- Multiple choice questions
- True/false questions
- SCORM score tracking

### Interactive Content
- Custom interactive elements
- Button interactions
- Form submissions

## SCORM Compliance

The generated SCORM packages are compliant with:
- **SCORM 1.2**: CAM 1.3 specification
- **SCORM 2004**: 4th Edition specification

### Features Included:
- Proper manifest structure (`imsmanifest.xml`)
- Content organization hierarchy
- Resource definitions and file references
- SCORM API integration for tracking
- Progress and completion tracking
- Score reporting capabilities

## Development

### File Structure
```
supabase/functions/scorm-converter/
├── index.ts                 # Main Edge Function handler
├── types.ts                 # TypeScript interfaces
├── utils.ts                 # Utility functions
├── validation.ts            # Input validation and error handling
├── to-scorm.ts             # Learning path to SCORM conversion
├── from-scorm.ts           # SCORM to learning path conversion
├── manifest-generator.ts    # SCORM manifest XML generation
└── README.md               # This documentation
```

### Testing

The function includes comprehensive validation and error handling. Test with various input formats to ensure proper conversion.

### Deployment

Deploy using the Supabase CLI:
```bash
supabase functions deploy scorm-converter
```

## Limitations

- YouTube video duration extraction requires additional API calls (not implemented)
- Schema files are placeholders (include actual XSD files in production)
- Complex SCORM sequencing rules are not fully supported
- Large content files may hit Edge Function size limits

## Contributing

When contributing to this function:
1. Maintain TypeScript type safety
2. Add comprehensive error handling
3. Include validation for new features
4. Update documentation and examples
5. Test both conversion directions
