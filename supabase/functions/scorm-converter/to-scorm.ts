// Learning Path to SCORM conversion logic

import {
  LearningPathFormat,
  SCORMFormat,
  SCORMManifest,
  SCORMOrganization,
  SCORMItem,
  SCORMResource,
  ConversionOptions,
  ConversionError
} from './types.ts';

import {
  UniqueIdGenerator,
  escapeXml,
  escapeHtml,
  wrapInXmlTag,
  parseDuration,
  formatSCORMDuration,
  parseYouTubeUrl,
  generateVideoHTML,
  calculateTotalDuration,
  createConversionError,
  logConversion
} from './utils.ts';

import {
  generateIMSManifest,
  validateManifestForXML,
  generatePackageStructure
} from './manifest-generator.ts';

/**
 * Converts learning path format to SCORM-compliant format
 */
export function convertToSCORM(
  learningPath: LearningPathFormat,
  options: ConversionOptions = {}
): SCORMFormat {
  logConversion('Starting learning path to SCORM conversion', { title: learningPath.title });

  try {
    const idGenerator = new UniqueIdGenerator();
    const scormVersion = options.scormVersion || '2004';
    
    // Generate course-level identifiers
    const courseId = idGenerator.generateCourseId(learningPath.title);
    const organizationId = idGenerator.generateOrganizationId(learningPath.title);
    
    // Calculate total duration
    const totalDuration = calculateTotalDuration(learningPath);
    
    // Generate SCORM manifest
    const manifest = generateSCORMManifest(
      learningPath,
      courseId,
      organizationId,
      idGenerator,
      totalDuration,
      options
    );

    // Validate manifest before generating content
    const manifestValidation = validateManifestForXML(manifest);
    if (!manifestValidation.isValid) {
      throw createConversionError(
        `Invalid manifest structure: ${manifestValidation.errors.join(', ')}`,
        'INVALID_MANIFEST',
        { manifest, errors: manifestValidation.errors }
      );
    }

    // Generate content files
    const contentFiles = generateContentFiles(learningPath, idGenerator, options);

    // Add the manifest XML file
    contentFiles['imsmanifest.xml'] = generateIMSManifest(manifest, options);

    // Add SCORM schema files
    addSCORMSchemaFiles(contentFiles);

    logConversion('SCORM conversion completed successfully', {
      manifestValid: true,
      fileCount: Object.keys(contentFiles).length,
      packageStructure: generatePackageStructure(manifest)
    });

    return {
      manifest,
      contentFiles
    };

  } catch (error) {
    logConversion('SCORM conversion failed', { error: error.message });
    throw createConversionError(
      `Failed to convert learning path to SCORM: ${error.message}`,
      'CONVERSION_FAILED',
      { learningPath, options },
      ['Check input data format', 'Verify all required fields are present']
    );
  }
}

/**
 * Generates the SCORM manifest structure
 */
function generateSCORMManifest(
  learningPath: LearningPathFormat,
  courseId: string,
  organizationId: string,
  idGenerator: UniqueIdGenerator,
  totalDuration: any,
  options: ConversionOptions
): SCORMManifest {
  
  const moduleNames = Object.keys(learningPath.journey);
  const items: SCORMItem[] = [];
  const resources: SCORMResource[] = [];

  // Add main index.html resource
  resources.push({
    identifier: idGenerator.generateResourceId('main', 0),
    type: 'webcontent',
    href: 'index.html',
    scormType: 'sco',
    files: ['index.html', 'styles.css', 'scorm-api.js'],
    metadata: {
      title: learningPath.title,
      description: `Main course content for ${learningPath.title}`,
      duration: totalDuration.formatted
    }
  });

  // Process each module
  moduleNames.forEach((moduleName, moduleIndex) => {
    const lessons = learningPath.journey[moduleName];
    const moduleId = idGenerator.generateModuleId(moduleName, moduleIndex);
    const moduleResourceId = idGenerator.generateResourceId('module', moduleIndex);

    // Create module item
    const moduleItem: SCORMItem = {
      identifier: moduleId,
      identifierref: moduleResourceId,
      title: moduleName,
      isvisible: true,
      children: []
    };

    // Add module resource
    resources.push({
      identifier: moduleResourceId,
      type: 'webcontent',
      href: `modules/module_${moduleIndex}.html`,
      scormType: 'sco',
      files: [`modules/module_${moduleIndex}.html`],
      metadata: {
        title: moduleName,
        description: `Module content for ${moduleName}`
      }
    });

    // Process lessons within module
    lessons.forEach((lesson, lessonIndex) => {
      const lessonId = idGenerator.generateLessonId(lesson.title, moduleIndex, lessonIndex);
      const lessonResourceId = idGenerator.generateResourceId('lesson', moduleIndex * 1000 + lessonIndex);

      // Create lesson item
      const lessonItem: SCORMItem = {
        identifier: lessonId,
        identifierref: lessonResourceId,
        title: lesson.title,
        isvisible: true,
        timelimitaction: 'continue,no message'
      };

      // Add lesson resource
      resources.push({
        identifier: lessonResourceId,
        type: 'webcontent',
        href: `lessons/lesson_${moduleIndex}_${lessonIndex}.html`,
        scormType: 'sco',
        files: [`lessons/lesson_${moduleIndex}_${lessonIndex}.html`],
        metadata: {
          title: lesson.title,
          description: `Lesson content for ${lesson.title}`,
          duration: lesson.duration
        }
      });

      moduleItem.children!.push(lessonItem);
    });

    items.push(moduleItem);
  });

  // Create organization
  const organization: SCORMOrganization = {
    identifier: organizationId,
    title: learningPath.title,
    structure: 'hierarchical',
    objectivesGlobalToSystem: false,
    items
  };

  // Create manifest
  const manifest: SCORMManifest = {
    identifier: courseId,
    version: '1.0',
    metadata: {
      schema: 'ADL SCORM',
      schemaversion: options.scormVersion === '1.2' ? 'CAM 1.3' : '2004 4th Edition',
      title: options.packageTitle || learningPath.title,
      description: options.packageDescription || `SCORM package for ${learningPath.title}`,
      language: options.language || 'en-US',
      author: options.author || 'Learnify Flow Studio',
      duration: formatSCORMDuration(totalDuration.totalSeconds)
    },
    organizations: {
      default: organizationId,
      organizations: [organization]
    },
    resources
  };

  return manifest;
}

/**
 * Generates content files for the SCORM package
 */
function generateContentFiles(
  learningPath: LearningPathFormat,
  idGenerator: UniqueIdGenerator,
  options: ConversionOptions
): { [filename: string]: string } {
  
  const files: { [filename: string]: string } = {};

  // Generate main index.html
  files['index.html'] = generateMainIndexHTML(learningPath, options);

  // Generate CSS styles
  files['styles.css'] = generateSCORMStyles();

  // Generate SCORM API JavaScript
  files['scorm-api.js'] = generateSCORMAPI();

  // Generate module files
  const moduleNames = Object.keys(learningPath.journey);
  moduleNames.forEach((moduleName, moduleIndex) => {
    files[`modules/module_${moduleIndex}.html`] = generateModuleHTML(
      moduleName,
      learningPath.journey[moduleName],
      moduleIndex,
      options
    );
  });

  // Generate lesson files
  moduleNames.forEach((moduleName, moduleIndex) => {
    const lessons = learningPath.journey[moduleName];
    lessons.forEach((lesson, lessonIndex) => {
      files[`lessons/lesson_${moduleIndex}_${lessonIndex}.html`] = generateLessonHTML(
        lesson,
        moduleIndex,
        lessonIndex,
        options
      );
    }

/**
 * Generates HTML for individual modules
 */
function generateModuleHTML(
  moduleName: string,
  lessons: any[],
  moduleIndex: number,
  options: ConversionOptions
): string {
  const lessonList = lessons.map((lesson, lessonIndex) => `
    <div class="lesson-item">
      <h3>${escapeHtml(lesson.title)}</h3>
      <p class="lesson-meta">
        <span class="lesson-type">${lesson.type}</span>
        <span class="lesson-duration">${lesson.duration}</span>
      </p>
      <a href="../lessons/lesson_${moduleIndex}_${lessonIndex}.html" class="lesson-link">
        Start Lesson
      </a>
    </div>
  `).join('');

  return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${escapeHtml(moduleName)}</title>
    <link rel="stylesheet" href="../styles.css">
    <script src="../scorm-api.js"></script>
</head>
<body>
    <div class="module-container">
        <header class="module-header">
            <h1>${escapeHtml(moduleName)}</h1>
            <p class="module-meta">${lessons.length} lessons</p>
        </header>

        <main class="module-content">
            <div class="lesson-grid">
                ${lessonList}
            </div>
        </main>
    </div>
</body>
</html>`;
}

/**
 * Generates HTML for individual lessons
 */
function generateLessonHTML(
  lesson: any,
  moduleIndex: number,
  lessonIndex: number,
  options: ConversionOptions
): string {
  const lessonId = `lesson_${moduleIndex}_${lessonIndex}`;
  const videoInfo = parseYouTubeUrl(lesson.link);

  let contentHTML = '';

  if (lesson.type === 'video' && videoInfo) {
    contentHTML = generateVideoHTML(lesson.title, lesson.link, lesson.duration, lesson.img_url);
  } else {
    contentHTML = `
      <div class="lesson-content">
        <h2>${escapeHtml(lesson.title)}</h2>
        <div class="content-block">
          <p>Content type: ${lesson.type}</p>
          <p>Duration: ${lesson.duration}</p>
          ${lesson.link ? `<a href="${lesson.link}" target="_blank" class="external-link">View Content</a>` : ''}
        </div>
      </div>
    `;
  }

  return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${escapeHtml(lesson.title)}</title>
    <link rel="stylesheet" href="../styles.css">
    <script src="../scorm-api.js"></script>
</head>
<body>
    <div class="lesson-container">
        ${contentHTML}

        <div class="lesson-actions">
            <button onclick="markLessonComplete('${lessonId}')" class="complete-btn">
                Mark Complete
            </button>
        </div>
    </div>

    <script>
        function markLessonComplete(lessonId) {
            // Update SCORM status
            if (typeof scormAPI !== 'undefined') {
                scormAPI.setStatus('completed');
                scormAPI.commit();
            }

            // Notify parent window
            if (window.parent) {
                window.parent.postMessage({
                    type: 'lesson_completed',
                    lessonId: lessonId
                }, '*');
            }

            // Visual feedback
            const button = document.querySelector('.complete-btn');
            if (button) {
                button.textContent = 'Completed!';
                button.disabled = true;
                button.style.backgroundColor = '#10b981';
            }
        }

        // Initialize SCORM for this lesson
        if (typeof scormAPI !== 'undefined') {
            scormAPI.initialize();
            scormAPI.setStatus('incomplete');
        }
    </script>
</body>
</html>`;
}

/**
 * Generates CSS styles for SCORM package
 */
function generateSCORMStyles(): string {
  return `/* SCORM Package Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #333;
    background: #f8fafc;
}

.course-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    background: white;
    min-height: 100vh;
}

.course-header {
    text-align: center;
    padding: 30px 0;
    border-bottom: 2px solid #e2e8f0;
    margin-bottom: 30px;
}

.course-header h1 {
    font-size: 2.5rem;
    color: #1a202c;
    margin-bottom: 15px;
}

.course-meta {
    display: flex;
    justify-content: center;
    gap: 20px;
    color: #718096;
    font-size: 0.9rem;
}

.course-layout {
    display: grid;
    grid-template-columns: 300px 1fr;
    gap: 30px;
    margin-bottom: 30px;
}

.course-navigation {
    background: #f7fafc;
    padding: 20px;
    border-radius: 8px;
    height: fit-content;
}

.course-navigation h2 {
    margin-bottom: 20px;
    color: #2d3748;
    font-size: 1.2rem;
}

.module-nav {
    margin-bottom: 20px;
}

.module-nav h3 {
    color: #4a5568;
    margin-bottom: 10px;
    padding-bottom: 5px;
    border-bottom: 1px solid #e2e8f0;
}

.lesson-list {
    list-style: none;
}

.lesson-list li {
    margin-bottom: 8px;
}

.lesson-list a {
    color: #4299e1;
    text-decoration: none;
    padding: 8px 12px;
    display: block;
    border-radius: 4px;
    transition: background 0.2s;
}

.lesson-list a:hover {
    background: #ebf8ff;
}

.content-frame {
    width: 100%;
    height: 600px;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
}

.course-progress {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    position: sticky;
    bottom: 20px;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #e2e8f0;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 10px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #48bb78, #38a169);
    width: 0%;
    transition: width 0.3s ease;
}

.progress-text {
    font-weight: 500;
    color: #4a5568;
}

/* Lesson Styles */
.lesson-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
}

.lesson-video {
    text-align: center;
    margin-bottom: 30px;
}

.lesson-video h2 {
    margin-bottom: 20px;
    color: #2d3748;
}

.video-container {
    position: relative;
    width: 100%;
    height: 0;
    padding-bottom: 56.25%;
    margin: 20px 0;
}

.video-iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 8px;
}

.video-duration {
    color: #718096;
    font-size: 0.9rem;
    margin-top: 10px;
}

.video-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 20px;
}

.lesson-actions {
    text-align: center;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #e2e8f0;
}

.complete-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 6px;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.2s;
    font-weight: 600;
}

.complete-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.complete-btn:disabled {
    background: #10b981;
    cursor: not-allowed;
    transform: none;
}

.external-link {
    background: #f8fafc;
    color: #4a5568;
    border: 2px solid #e2e8f0;
    padding: 10px 20px;
    border-radius: 6px;
    text-decoration: none;
    font-size: 0.9rem;
    transition: all 0.2s;
    display: inline-flex;
    align-items: center;
    gap: 5px;
}

.external-link:hover {
    background: #e2e8f0;
    border-color: #cbd5e0;
    transform: translateY(-1px);
}

/* Module Styles */
.module-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
}

.module-header {
    text-align: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid #e2e8f0;
}

.module-header h1 {
    color: #2d3748;
    margin-bottom: 10px;
}

.module-meta {
    color: #718096;
}

.lesson-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.lesson-item {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border: 1px solid #e2e8f0;
}

.lesson-item h3 {
    color: #2d3748;
    margin-bottom: 10px;
}

.lesson-meta {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
    font-size: 0.9rem;
    color: #718096;
}

.lesson-type {
    background: #e2e8f0;
    padding: 2px 8px;
    border-radius: 4px;
    text-transform: capitalize;
}

.lesson-link {
    background: #4299e1;
    color: white;
    padding: 8px 16px;
    border-radius: 4px;
    text-decoration: none;
    display: inline-block;
    transition: background 0.2s;
}

.lesson-link:hover {
    background: #3182ce;
}`;
}

/**
 * Generates SCORM API JavaScript
 */
function generateSCORMAPI(): string {
  return `// SCORM API Implementation
var scormAPI = {
    initialized: false,

    initialize: function() {
        this.initialized = true;
        console.log('SCORM API initialized');
        return 'true';
    },

    setStatus: function(status) {
        if (!this.initialized) return 'false';
        localStorage.setItem('scorm_status', status);
        console.log('SCORM status set to:', status);
        return 'true';
    },

    getStatus: function() {
        if (!this.initialized) return 'false';
        return localStorage.getItem('scorm_status') || 'not attempted';
    },

    setProgress: function(progress) {
        if (!this.initialized) return 'false';
        localStorage.setItem('scorm_progress', progress.toString());
        console.log('SCORM progress set to:', progress);
        return 'true';
    },

    getProgress: function() {
        if (!this.initialized) return '0';
        return localStorage.getItem('scorm_progress') || '0';
    },

    setScore: function(score) {
        if (!this.initialized) return 'false';
        localStorage.setItem('scorm_score', score.toString());
        console.log('SCORM score set to:', score);
        return 'true';
    },

    getScore: function() {
        if (!this.initialized) return '0';
        return localStorage.getItem('scorm_score') || '0';
    },

    commit: function() {
        if (!this.initialized) return 'false';
        console.log('SCORM data committed');
        return 'true';
    },

    terminate: function() {
        this.initialized = false;
        console.log('SCORM API terminated');
        return 'true';
    }
};

// Make API available globally
window.scormAPI = scormAPI;`;
}

/**
 * Adds SCORM schema files to the content files
 */
function addSCORMSchemaFiles(contentFiles: { [filename: string]: string }): void {
  const schemaFiles = [
    'adlcp_v1p3.xsd',
    'adlnav_v1p3.xsd',
    'adlseq_v1p3.xsd',
    'imscp_v1p1.xsd',
    'imsss_v1p0.xsd'
  ];

  // Add placeholder schema files
  // In production, these would be the actual XSD schema files
  schemaFiles.forEach(fileName => {
    contentFiles[fileName] = `<?xml version="1.0" encoding="UTF-8"?>
<!-- ${fileName} - SCORM Schema File -->
<!-- This is a placeholder. In production, include the actual XSD schema content -->
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <!-- Schema content would go here -->
</xs:schema>`;
  });
});
  });

  return files;
}

/**
 * Generates the main index.html file
 */
function generateMainIndexHTML(
  learningPath: LearningPathFormat,
  options: ConversionOptions
): string {
  const moduleNames = Object.keys(learningPath.journey);
  
  const navigationHTML = moduleNames.map((moduleName, moduleIndex) => {
    const lessons = learningPath.journey[moduleName];
    const lessonLinks = lessons.map((lesson, lessonIndex) => 
      `<li><a href="lessons/lesson_${moduleIndex}_${lessonIndex}.html" target="contentFrame">${escapeHtml(lesson.title)}</a></li>`
    ).join('');

    return `
      <div class="module-nav">
        <h3>${escapeHtml(moduleName)}</h3>
        <ul class="lesson-list">
          ${lessonLinks}
        </ul>
      </div>
    `;
  }).join('');

  return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${escapeHtml(learningPath.title)}</title>
    <link rel="stylesheet" href="styles.css">
    <script src="scorm-api.js"></script>
</head>
<body>
    <div class="course-container">
        <header class="course-header">
            <h1>${escapeHtml(learningPath.title)}</h1>
            <div class="course-meta">
                <span class="module-count">${moduleNames.length} Modules</span>
                <span class="lesson-count">${Object.values(learningPath.journey).flat().length} Lessons</span>
            </div>
        </header>

        <div class="course-layout">
            <nav class="course-navigation">
                <h2>Course Content</h2>
                ${navigationHTML}
            </nav>

            <main class="course-content">
                <iframe name="contentFrame" src="lessons/lesson_0_0.html" class="content-frame"></iframe>
            </main>
        </div>

        <div class="course-progress">
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <span class="progress-text" id="progressText">0% Complete</span>
        </div>
    </div>

    <script>
        // Initialize SCORM
        if (typeof scormAPI !== 'undefined') {
            scormAPI.initialize();
        }

        // Track progress
        let completedLessons = new Set();
        const totalLessons = ${Object.values(learningPath.journey).flat().length};

        function updateProgress() {
            const progress = Math.round((completedLessons.size / totalLessons) * 100);
            document.getElementById('progressFill').style.width = progress + '%';
            document.getElementById('progressText').textContent = progress + '% Complete';
            
            if (typeof scormAPI !== 'undefined') {
                scormAPI.setProgress(progress);
                if (progress === 100) {
                    scormAPI.setStatus('completed');
                }
            }
        }

        // Listen for lesson completion messages
        window.addEventListener('message', function(event) {
            if (event.data.type === 'lesson_completed') {
                completedLessons.add(event.data.lessonId);
                updateProgress();
            }
        });
    </script>
</body>
</html>`;
}
