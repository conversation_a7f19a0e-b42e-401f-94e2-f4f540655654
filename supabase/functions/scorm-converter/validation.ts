// Comprehensive validation and error handling for SCORM conversion

import {
  LearningPathFormat,
  SCORMFormat,
  ConversionRequest,
  ValidationResult,
  ValidationContext,
  ConversionError,
  ValidationError
} from './types.ts';

import { validateLearningPath } from './utils.ts';

// ============================================================================
// REQUEST VALIDATION
// ============================================================================

/**
 * Validates the incoming conversion request
 */
export function validateConversionRequest(body: any): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Check if body exists
  if (!body || typeof body !== 'object') {
    errors.push('Request body must be a valid JSON object');
    return { isValid: false, errors, warnings };
  }

  // Validate conversion type
  if (!body.type) {
    errors.push('Conversion type is required');
  } else if (!['to-scorm', 'from-scorm'].includes(body.type)) {
    errors.push('Conversion type must be either "to-scorm" or "from-scorm"');
  }

  // Validate data field
  if (!body.data) {
    errors.push('Data field is required');
  } else if (typeof body.data !== 'object') {
    errors.push('Data field must be an object');
  }

  // Validate options if present
  if (body.options && typeof body.options !== 'object') {
    warnings.push('Options field should be an object if provided');
  }

  return { isValid: errors.length === 0, errors, warnings };
}

/**
 * Validates conversion options
 */
export function validateConversionOptions(options: any): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  if (!options || typeof options !== 'object') {
    return { isValid: true, errors, warnings }; // Options are optional
  }

  // Validate SCORM version
  if (options.scormVersion && !['1.2', '2004'].includes(options.scormVersion)) {
    errors.push('SCORM version must be either "1.2" or "2004"');
  }

  // Validate boolean options
  const booleanOptions = ['includeVideos', 'generateUniqueIds', 'preserveStructure'];
  booleanOptions.forEach(option => {
    if (options[option] !== undefined && typeof options[option] !== 'boolean') {
      warnings.push(`Option "${option}" should be a boolean value`);
    }
  });

  // Validate string options
  const stringOptions = ['packageTitle', 'packageDescription', 'language', 'author'];
  stringOptions.forEach(option => {
    if (options[option] !== undefined && typeof options[option] !== 'string') {
      warnings.push(`Option "${option}" should be a string value`);
    }
  });

  return { isValid: errors.length === 0, errors, warnings };
}

// ============================================================================
// FORMAT-SPECIFIC VALIDATION
// ============================================================================

/**
 * Validates SCORM format data structure
 */
export function validateSCORMFormat(data: any): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  if (!data || typeof data !== 'object') {
    errors.push('SCORM data must be an object');
    return { isValid: false, errors, warnings };
  }

  // Validate manifest
  if (!data.manifest) {
    errors.push('SCORM manifest is required');
  } else {
    const manifestValidation = validateSCORMManifest(data.manifest);
    errors.push(...manifestValidation.errors);
    warnings.push(...manifestValidation.warnings);
  }

  // Validate content files
  if (!data.contentFiles) {
    errors.push('SCORM content files are required');
  } else if (typeof data.contentFiles !== 'object') {
    errors.push('SCORM content files must be an object');
  } else {
    const contentValidation = validateSCORMContentFiles(data.contentFiles);
    errors.push(...contentValidation.errors);
    warnings.push(...contentValidation.warnings);
  }

  return { isValid: errors.length === 0, errors, warnings };
}

/**
 * Validates SCORM manifest structure
 */
function validateSCORMManifest(manifest: any): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  if (!manifest || typeof manifest !== 'object') {
    errors.push('SCORM manifest must be an object');
    return { isValid: false, errors, warnings };
  }

  // Required fields
  const requiredFields = ['identifier', 'version', 'metadata', 'organizations', 'resources'];
  requiredFields.forEach(field => {
    if (!manifest[field]) {
      errors.push(`Manifest field "${field}" is required`);
    }
  });

  // Validate metadata
  if (manifest.metadata) {
    if (!manifest.metadata.title) {
      warnings.push('Manifest metadata should include a title');
    }
    if (!manifest.metadata.schema) {
      warnings.push('Manifest metadata should include schema information');
    }
  }

  // Validate organizations
  if (manifest.organizations) {
    if (!manifest.organizations.default) {
      errors.push('Organizations must specify a default organization');
    }
    if (!Array.isArray(manifest.organizations.organizations)) {
      errors.push('Organizations must contain an array of organization objects');
    } else if (manifest.organizations.organizations.length === 0) {
      errors.push('At least one organization is required');
    }
  }

  // Validate resources
  if (manifest.resources) {
    if (!Array.isArray(manifest.resources)) {
      errors.push('Resources must be an array');
    } else if (manifest.resources.length === 0) {
      warnings.push('No resources found in manifest');
    }
  }

  return { isValid: errors.length === 0, errors, warnings };
}

/**
 * Validates SCORM content files
 */
function validateSCORMContentFiles(contentFiles: any): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  if (!contentFiles || typeof contentFiles !== 'object') {
    errors.push('Content files must be an object');
    return { isValid: false, errors, warnings };
  }

  const fileNames = Object.keys(contentFiles);
  if (fileNames.length === 0) {
    errors.push('At least one content file is required');
    return { isValid: false, errors, warnings };
  }

  // Check for required files
  const hasIndexFile = fileNames.some(name => 
    name.toLowerCase().includes('index') || name.toLowerCase().includes('main')
  );
  if (!hasIndexFile) {
    warnings.push('No main index file found in content files');
  }

  // Validate file contents
  fileNames.forEach(fileName => {
    const content = contentFiles[fileName];
    if (typeof content !== 'string') {
      errors.push(`Content file "${fileName}" must contain string content`);
    } else if (content.trim().length === 0) {
      warnings.push(`Content file "${fileName}" is empty`);
    }
  });

  return { isValid: errors.length === 0, errors, warnings };
}

// ============================================================================
// ERROR HANDLING
// ============================================================================

/**
 * Creates standardized error responses
 */
export function createErrorResponse(
  error: string | Error | ConversionError,
  statusCode: number = 500,
  context?: any
): Response {
  
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
    'Access-Control-Allow-Methods': 'POST, OPTIONS',
    'Content-Type': 'application/json'
  };

  let errorMessage: string;
  let errorCode: string = 'UNKNOWN_ERROR';
  let suggestions: string[] = [];
  let details: any = undefined;

  if (typeof error === 'string') {
    errorMessage = error;
  } else if (error instanceof Error) {
    errorMessage = error.message;
    
    if ('code' in error) {
      errorCode = (error as ConversionError).code;
    }
    
    if ('suggestions' in error) {
      suggestions = (error as ConversionError).suggestions || [];
    }
    
    if ('context' in error) {
      details = (error as ConversionError).context;
    }
  } else {
    errorMessage = 'An unknown error occurred';
  }

  const responseBody = {
    success: false,
    error: errorMessage,
    code: errorCode,
    details: context || details,
    suggestions: suggestions.length > 0 ? suggestions : undefined,
    timestamp: new Date().toISOString()
  };

  // Log error for debugging
  console.error('Error Response:', JSON.stringify(responseBody, null, 2));

  return new Response(
    JSON.stringify(responseBody),
    { 
      status: statusCode, 
      headers: corsHeaders 
    }
  );
}

/**
 * Creates standardized success responses
 */
export function createSuccessResponse(
  data: any,
  format: 'scorm' | 'learning_path',
  warnings?: string[]
): Response {
  
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
    'Access-Control-Allow-Methods': 'POST, OPTIONS',
    'Content-Type': 'application/json'
  };

  const responseBody = {
    success: true,
    data,
    format,
    warnings: warnings && warnings.length > 0 ? warnings : undefined,
    timestamp: new Date().toISOString()
  };

  // Log success for debugging
  console.log('Success Response:', JSON.stringify({
    success: true,
    format,
    dataKeys: typeof data === 'object' ? Object.keys(data) : 'non-object',
    warningCount: warnings?.length || 0
  }, null, 2));

  return new Response(
    JSON.stringify(responseBody),
    { 
      status: 200, 
      headers: corsHeaders 
    }
  );
}

// ============================================================================
// VALIDATION HELPERS
// ============================================================================

/**
 * Validates and sanitizes input data based on conversion type
 */
export function validateAndSanitizeInput(
  type: 'to-scorm' | 'from-scorm',
  data: any
): { isValid: boolean; sanitizedData?: any; errors: string[]; warnings: string[] } {
  
  let validationResult: ValidationResult;
  let sanitizedData: any = data;

  if (type === 'to-scorm') {
    validationResult = validateLearningPath(data);
    
    // Sanitize learning path data
    if (validationResult.isValid) {
      sanitizedData = sanitizeLearningPath(data);
    }
  } else {
    validationResult = validateSCORMFormat(data);
    
    // Sanitize SCORM data
    if (validationResult.isValid) {
      sanitizedData = sanitizeSCORMData(data);
    }
  }

  return {
    isValid: validationResult.isValid,
    sanitizedData: validationResult.isValid ? sanitizedData : undefined,
    errors: validationResult.errors,
    warnings: validationResult.warnings
  };
}

/**
 * Sanitizes learning path data
 */
function sanitizeLearningPath(data: LearningPathFormat): LearningPathFormat {
  const sanitized: LearningPathFormat = {
    title: data.title.trim(),
    img_url: data.img_url.trim(),
    journey: {}
  };

  // Sanitize journey data
  Object.entries(data.journey).forEach(([moduleName, lessons]) => {
    const sanitizedModuleName = moduleName.trim();
    sanitized.journey[sanitizedModuleName] = lessons.map(lesson => ({
      title: lesson.title.trim(),
      link: lesson.link.trim(),
      type: lesson.type,
      duration: lesson.duration.trim(),
      ...(lesson.img_url && { img_url: lesson.img_url.trim() }),
      ...(lesson.description && { description: lesson.description.trim() })
    }));
  });

  return sanitized;
}

/**
 * Sanitizes SCORM data
 */
function sanitizeSCORMData(data: SCORMFormat): SCORMFormat {
  // For SCORM data, we mainly need to ensure content files are properly formatted
  const sanitized: SCORMFormat = {
    manifest: data.manifest, // Manifest structure is complex, leave as-is for now
    contentFiles: {}
  };

  // Sanitize content files
  Object.entries(data.contentFiles).forEach(([filename, content]) => {
    const sanitizedFilename = filename.trim();
    const sanitizedContent = typeof content === 'string' ? content : String(content);
    sanitized.contentFiles[sanitizedFilename] = sanitizedContent;
  });

  return sanitized;
}

/**
 * Logs validation results for debugging
 */
export function logValidationResult(
  stage: string,
  result: ValidationResult,
  context?: any
): void {
  const timestamp = new Date().toISOString();
  
  console.log(`[${timestamp}] Validation - ${stage}:`, {
    isValid: result.isValid,
    errorCount: result.errors.length,
    warningCount: result.warnings.length,
    errors: result.errors,
    warnings: result.warnings,
    context: context ? JSON.stringify(context, null, 2) : undefined
  });
}
