// SCORM to Learning Path conversion logic

import {
  LearningPathFormat,
  LearningPathLesson,
  SCORMFormat,
  SCORMManifest,
  SCORMItem,
  SCORMResource,
  ConversionOptions,
  ConversionError
} from './types.ts';

import {
  parseDuration,
  parseYouTubeUrl,
  createConversionError,
  logConversion
} from './utils.ts';

/**
 * Converts SCORM format to learning path format
 */
export function convertFromSCORM(
  scormData: SCORMFormat,
  options: ConversionOptions = {}
): LearningPathFormat {
  logConversion('Starting SCORM to learning path conversion');

  try {
    const manifest = scormData.manifest;
    
    // Extract basic course information
    const title = manifest.metadata.title || 'Untitled Course';
    const img_url = extractCourseImageUrl(scormData) || 'https://via.placeholder.com/400x300?text=Course+Image';
    
    // Process organizations to extract course structure
    const journey = processOrganizations(manifest, scormData, options);
    
    const learningPath: LearningPathFormat = {
      title,
      img_url,
      journey
    };

    logConversion('SCORM to learning path conversion completed successfully');
    return learningPath;

  } catch (error) {
    logConversion('SCORM to learning path conversion failed', { error: error.message });
    throw createConversionError(
      `Failed to convert SCORM to learning path: ${error.message}`,
      'CONVERSION_FAILED',
      { scormData, options },
      ['Check SCORM manifest structure', 'Verify organization hierarchy']
    );
  }
}

/**
 * Processes SCORM organizations to extract course structure
 */
function processOrganizations(
  manifest: SCORMManifest,
  scormData: SCORMFormat,
  options: ConversionOptions
): { [moduleName: string]: LearningPathLesson[] } {
  
  const journey: { [moduleName: string]: LearningPathLesson[] } = {};
  
  if (!manifest.organizations || !manifest.organizations.organizations) {
    throw new Error('No organizations found in SCORM manifest');
  }

  // Get the default organization
  const defaultOrgId = manifest.organizations.default;
  const organization = manifest.organizations.organizations.find(
    org => org.identifier === defaultOrgId
  ) || manifest.organizations.organizations[0];

  if (!organization) {
    throw new Error('No valid organization found in SCORM manifest');
  }

  // Process organization items (modules/lessons)
  organization.items.forEach((item, index) => {
    if (item.children && item.children.length > 0) {
      // This is a module with lessons
      const moduleName = item.title || `Module ${index + 1}`;
      const lessons = processLessons(item.children, manifest.resources, scormData, options);
      if (lessons.length > 0) {
        journey[moduleName] = lessons;
      }
    } else {
      // This is a standalone lesson - create a default module
      const moduleName = 'Main Content';
      const lesson = processLesson(item, manifest.resources, scormData, options);
      if (lesson) {
        if (!journey[moduleName]) {
          journey[moduleName] = [];
        }
        journey[moduleName].push(lesson);
      }
    }
  });

  return journey;
}

/**
 * Processes SCORM items to extract lessons
 */
function processLessons(
  items: SCORMItem[],
  resources: SCORMResource[],
  scormData: SCORMFormat,
  options: ConversionOptions
): LearningPathLesson[] {
  
  const lessons: LearningPathLesson[] = [];

  items.forEach(item => {
    const lesson = processLesson(item, resources, scormData, options);
    if (lesson) {
      lessons.push(lesson);
    }
  });

  return lessons;
}

/**
 * Processes a single SCORM item to extract lesson information
 */
function processLesson(
  item: SCORMItem,
  resources: SCORMResource[],
  scormData: SCORMFormat,
  options: ConversionOptions
): LearningPathLesson | null {
  
  if (!item.identifierref) {
    // Item without resource reference - skip
    return null;
  }

  // Find the associated resource
  const resource = resources.find(r => r.identifier === item.identifierref);
  if (!resource) {
    console.warn(`Resource not found for item: ${item.identifier}`);
    return null;
  }

  // Extract lesson information
  const title = item.title || 'Untitled Lesson';
  const duration = extractDuration(resource, scormData) || '5:00';
  const link = extractContentLink(resource, scormData) || '#';
  const type = determineContentType(resource, scormData);
  const img_url = extractLessonImageUrl(resource, scormData);

  const lesson: LearningPathLesson = {
    title,
    link,
    type,
    duration
  };

  if (img_url) {
    lesson.img_url = img_url;
  }

  return lesson;
}

/**
 * Extracts duration from SCORM resource or content
 */
function extractDuration(
  resource: SCORMResource,
  scormData: SCORMFormat
): string | null {
  
  // Check resource metadata first
  if (resource.metadata?.duration) {
    return resource.metadata.duration;
  }

  // Try to extract from content files
  if (resource.href && scormData.contentFiles[resource.href]) {
    const content = scormData.contentFiles[resource.href];
    
    // Look for duration patterns in HTML content
    const durationRegex = /duration[:\s]*["']?(\d+:\d+(?::\d+)?)["']?/i;
    const match = content.match(durationRegex);
    if (match) {
      return match[1];
    }

    // Look for video duration in YouTube embeds
    const youtubeRegex = /youtube\.com\/embed\/([^"'?\s]+)/i;
    const youtubeMatch = content.match(youtubeRegex);
    if (youtubeMatch) {
      // For YouTube videos, we can't extract duration from embed URL
      // Return a default duration
      return '10:00';
    }
  }

  return null;
}

/**
 * Extracts content link from SCORM resource
 */
function extractContentLink(
  resource: SCORMResource,
  scormData: SCORMFormat
): string | null {
  
  if (!resource.href || !scormData.contentFiles[resource.href]) {
    return null;
  }

  const content = scormData.contentFiles[resource.href];

  // Look for YouTube video links
  const youtubeRegex = /(?:youtube\.com\/(?:watch\?v=|embed\/)|youtu\.be\/)([^"'?\s&]+)/i;
  const youtubeMatch = content.match(youtubeRegex);
  if (youtubeMatch) {
    const videoId = youtubeMatch[1];
    return `https://www.youtube.com/watch?v=${videoId}`;
  }

  // Look for other video links
  const videoRegex = /(?:src|href)=["']([^"']*\.(?:mp4|webm|ogg|avi|mov))["']/i;
  const videoMatch = content.match(videoRegex);
  if (videoMatch) {
    return videoMatch[1];
  }

  // Look for external links
  const linkRegex = /<a[^>]+href=["']([^"']+)["'][^>]*target=["']_blank["']/i;
  const linkMatch = content.match(linkRegex);
  if (linkMatch) {
    return linkMatch[1];
  }

  // Default to the resource href
  return resource.href;
}

/**
 * Determines content type from SCORM resource
 */
function determineContentType(
  resource: SCORMResource,
  scormData: SCORMFormat
): 'video' | 'text' | 'quiz' | 'interactive' {
  
  if (!resource.href || !scormData.contentFiles[resource.href]) {
    return 'text';
  }

  const content = scormData.contentFiles[resource.href].toLowerCase();

  // Check for video content
  if (content.includes('youtube.com') || 
      content.includes('youtu.be') || 
      content.includes('video') ||
      content.includes('iframe') ||
      content.includes('.mp4') ||
      content.includes('.webm')) {
    return 'video';
  }

  // Check for quiz content
  if (content.includes('quiz') ||
      content.includes('question') ||
      content.includes('radio') ||
      content.includes('checkbox') ||
      content.includes('input type="radio"')) {
    return 'quiz';
  }

  // Check for interactive content
  if (content.includes('interactive') ||
      content.includes('onclick') ||
      content.includes('button') ||
      content.includes('form')) {
    return 'interactive';
  }

  // Default to text
  return 'text';
}

/**
 * Extracts course image URL from SCORM data
 */
function extractCourseImageUrl(scormData: SCORMFormat): string | null {
  // Look through content files for course images
  for (const [filename, content] of Object.entries(scormData.contentFiles)) {
    if (filename === 'index.html' || filename.includes('main')) {
      // Look for course header images
      const imgRegex = /<img[^>]+src=["']([^"']+)["'][^>]*(?:class=["'][^"']*(?:course|header|banner)[^"']*["']|alt=["'][^"']*(?:course|header|banner)[^"']*["'])/i;
      const match = content.match(imgRegex);
      if (match) {
        return match[1];
      }
    }
  }

  return null;
}

/**
 * Extracts lesson image URL from SCORM resource
 */
function extractLessonImageUrl(
  resource: SCORMResource,
  scormData: SCORMFormat
): string | null {
  
  if (!resource.href || !scormData.contentFiles[resource.href]) {
    return null;
  }

  const content = scormData.contentFiles[resource.href];

  // Look for YouTube thumbnail
  const youtubeRegex = /youtube\.com\/(?:watch\?v=|embed\/)([^"'?\s&]+)/i;
  const youtubeMatch = content.match(youtubeRegex);
  if (youtubeMatch) {
    const videoId = youtubeMatch[1];
    return `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;
  }

  // Look for lesson images
  const imgRegex = /<img[^>]+src=["']([^"']+)["']/i;
  const imgMatch = content.match(imgRegex);
  if (imgMatch) {
    return imgMatch[1];
  }

  return null;
}

/**
 * Validates SCORM format before conversion
 */
export function validateSCORMFormat(scormData: any): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (!scormData || typeof scormData !== 'object') {
    errors.push('SCORM data must be an object');
    return { isValid: false, errors };
  }

  if (!scormData.manifest) {
    errors.push('SCORM manifest is required');
    return { isValid: false, errors };
  }

  const manifest = scormData.manifest;

  if (!manifest.identifier) {
    errors.push('Manifest identifier is required');
  }

  if (!manifest.metadata) {
    errors.push('Manifest metadata is required');
  }

  if (!manifest.organizations) {
    errors.push('Manifest organizations are required');
  }

  if (!manifest.resources) {
    errors.push('Manifest resources are required');
  }

  if (!scormData.contentFiles || typeof scormData.contentFiles !== 'object') {
    errors.push('Content files are required');
  }

  return { isValid: errors.length === 0, errors };
}
