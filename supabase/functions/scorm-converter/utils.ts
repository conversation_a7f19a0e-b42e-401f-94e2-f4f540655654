// Utility functions for SCORM conversion

import { 
  YouTubeVideoInfo, 
  DurationInfo, 
  ValidationResult, 
  ValidationContext,
  ConversionError,
  LearningPathFormat,
  SCORMFormat
} from './types.ts';

// ============================================================================
// XML GENERATION UTILITIES
// ============================================================================

/**
 * Escapes XML special characters
 */
export function escapeXml(text: string): string {
  if (!text) return '';
  return text
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&apos;');
}

/**
 * Escapes HTML special characters
 */
export function escapeHtml(text: string): string {
  if (!text) return '';
  return text
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#39;');
}

/**
 * Generates XML attributes string from object
 */
export function generateXmlAttributes(attributes: Record<string, string | number | boolean>): string {
  return Object.entries(attributes)
    .filter(([_, value]) => value !== undefined && value !== null)
    .map(([key, value]) => `${key}="${escapeXml(String(value))}"`)
    .join(' ');
}

/**
 * Wraps content in XML tags with optional attributes
 */
export function wrapInXmlTag(
  tagName: string, 
  content: string, 
  attributes?: Record<string, string | number | boolean>
): string {
  const attrs = attributes ? ' ' + generateXmlAttributes(attributes) : '';
  if (!content.trim()) {
    return `<${tagName}${attrs} />`;
  }
  return `<${tagName}${attrs}>${content}</${tagName}>`;
}

// ============================================================================
// UNIQUE ID GENERATION
// ============================================================================

/**
 * Generates SCORM-compliant unique identifiers
 */
export class UniqueIdGenerator {
  private usedIds = new Set<string>();

  /**
   * Sanitizes text for use in IDs
   */
  private sanitizeForId(text: string): string {
    return text
      .replace(/[^a-zA-Z0-9\s-_]/g, '')
      .replace(/\s+/g, '_')
      .toLowerCase()
      .substring(0, 50);
  }

  /**
   * Ensures ID is unique by appending number if needed
   */
  private ensureUnique(baseId: string): string {
    let id = baseId;
    let counter = 1;
    
    while (this.usedIds.has(id)) {
      id = `${baseId}_${counter}`;
      counter++;
    }
    
    this.usedIds.add(id);
    return id;
  }

  generateCourseId(title: string): string {
    const baseId = `course_${this.sanitizeForId(title)}`;
    return this.ensureUnique(baseId);
  }

  generateModuleId(moduleTitle: string, index: number): string {
    const baseId = `module_${index}_${this.sanitizeForId(moduleTitle)}`;
    return this.ensureUnique(baseId);
  }

  generateLessonId(lessonTitle: string, moduleIndex: number, lessonIndex: number): string {
    const baseId = `lesson_${moduleIndex}_${lessonIndex}_${this.sanitizeForId(lessonTitle)}`;
    return this.ensureUnique(baseId);
  }

  generateResourceId(resourceType: string, index: number): string {
    const baseId = `resource_${resourceType}_${index}`;
    return this.ensureUnique(baseId);
  }

  generateOrganizationId(title: string): string {
    const baseId = `org_${this.sanitizeForId(title)}`;
    return this.ensureUnique(baseId);
  }

  generateItemId(title: string, index: number): string {
    const baseId = `item_${index}_${this.sanitizeForId(title)}`;
    return this.ensureUnique(baseId);
  }

  reset(): void {
    this.usedIds.clear();
  }
}

// ============================================================================
// DURATION PARSING AND FORMATTING
// ============================================================================

/**
 * Parses duration string into structured format
 */
export function parseDuration(duration: string): DurationInfo {
  if (!duration) {
    return { totalSeconds: 0, formatted: '00:00', hours: 0, minutes: 0, seconds: 0 };
  }

  // Handle different duration formats
  const timeRegex = /^(?:(\d+):)?(\d+):(\d+)$/; // HH:MM:SS or MM:SS
  const match = duration.match(timeRegex);

  if (!match) {
    console.warn(`Invalid duration format: ${duration}`);
    return { totalSeconds: 0, formatted: '00:00', hours: 0, minutes: 0, seconds: 0 };
  }

  const hours = parseInt(match[1] || '0', 10);
  const minutes = parseInt(match[2], 10);
  const seconds = parseInt(match[3], 10);

  const totalSeconds = hours * 3600 + minutes * 60 + seconds;
  const formatted = hours > 0 
    ? `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
    : `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

  return { totalSeconds, formatted, hours, minutes, seconds };
}

/**
 * Converts seconds to SCORM duration format (PT[H]H[M]M[S]S)
 */
export function formatSCORMDuration(totalSeconds: number): string {
  const hours = Math.floor(totalSeconds / 3600);
  const minutes = Math.floor((totalSeconds % 3600) / 60);
  const seconds = totalSeconds % 60;

  let duration = 'PT';
  if (hours > 0) duration += `${hours}H`;
  if (minutes > 0) duration += `${minutes}M`;
  if (seconds > 0) duration += `${seconds}S`;
  
  return duration === 'PT' ? 'PT0S' : duration;
}

// ============================================================================
// YOUTUBE LINK HANDLING
// ============================================================================

/**
 * Extracts YouTube video information from various URL formats
 */
export function parseYouTubeUrl(url: string): YouTubeVideoInfo | null {
  if (!url) return null;

  // YouTube watch URL pattern
  const watchRegex = /(?:youtube\.com\/watch\?v=|youtu\.be\/)([^&\n?#]+)/;
  const embedRegex = /youtube\.com\/embed\/([^&\n?#]+)/;
  
  let videoId: string | null = null;
  
  const watchMatch = url.match(watchRegex);
  const embedMatch = url.match(embedRegex);
  
  if (watchMatch) {
    videoId = watchMatch[1];
  } else if (embedMatch) {
    videoId = embedMatch[1];
  }

  if (!videoId) return null;

  return {
    videoId,
    embedUrl: `https://www.youtube.com/embed/${videoId}`,
    watchUrl: `https://www.youtube.com/watch?v=${videoId}`,
    thumbnailUrl: `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`
  };
}

/**
 * Converts YouTube watch URL to embed URL
 */
export function convertToEmbedUrl(url: string): string {
  const videoInfo = parseYouTubeUrl(url);
  return videoInfo ? videoInfo.embedUrl : url;
}

// ============================================================================
// VALIDATION UTILITIES
// ============================================================================

/**
 * Validates learning path format
 */
export function validateLearningPath(data: any): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  if (!data || typeof data !== 'object') {
    errors.push('Data must be an object');
    return { isValid: false, errors, warnings };
  }

  // Validate required fields
  if (!data.title || typeof data.title !== 'string') {
    errors.push('Title is required and must be a string');
  }

  if (!data.img_url || typeof data.img_url !== 'string') {
    warnings.push('Image URL is missing or invalid');
  }

  if (!data.journey || typeof data.journey !== 'object') {
    errors.push('Journey is required and must be an object');
    return { isValid: false, errors, warnings };
  }

  // Validate journey structure
  const moduleNames = Object.keys(data.journey);
  if (moduleNames.length === 0) {
    errors.push('Journey must contain at least one module');
  }

  for (const moduleName of moduleNames) {
    const lessons = data.journey[moduleName];
    
    if (!Array.isArray(lessons)) {
      errors.push(`Module "${moduleName}" must contain an array of lessons`);
      continue;
    }

    if (lessons.length === 0) {
      warnings.push(`Module "${moduleName}" has no lessons`);
      continue;
    }

    lessons.forEach((lesson: any, index: number) => {
      if (!lesson || typeof lesson !== 'object') {
        errors.push(`Lesson ${index} in module "${moduleName}" must be an object`);
        return;
      }

      if (!lesson.title || typeof lesson.title !== 'string') {
        errors.push(`Lesson ${index} in module "${moduleName}" must have a title`);
      }

      if (!lesson.link || typeof lesson.link !== 'string') {
        errors.push(`Lesson ${index} in module "${moduleName}" must have a link`);
      }

      if (!lesson.type || !['video', 'text', 'quiz', 'interactive'].includes(lesson.type)) {
        warnings.push(`Lesson ${index} in module "${moduleName}" has invalid or missing type`);
      }

      if (!lesson.duration || typeof lesson.duration !== 'string') {
        warnings.push(`Lesson ${index} in module "${moduleName}" has invalid or missing duration`);
      }
    });
  }

  return { isValid: errors.length === 0, errors, warnings };
}

/**
 * Creates a conversion error with context
 */
export function createConversionError(
  message: string, 
  code: string, 
  context?: any, 
  suggestions?: string[]
): ConversionError {
  const error = new Error(message) as ConversionError;
  error.code = code;
  error.context = context;
  error.suggestions = suggestions;
  return error;
}

/**
 * Logs conversion progress and errors
 */
export function logConversion(stage: string, data?: any): void {
  const timestamp = new Date().toISOString();
  console.log(`[${timestamp}] SCORM Conversion - ${stage}`, data ? JSON.stringify(data, null, 2) : '');
}

// ============================================================================
// CONTENT PROCESSING UTILITIES
// ============================================================================

/**
 * Generates HTML content for video lessons
 */
export function generateVideoHTML(
  title: string, 
  videoUrl: string, 
  duration?: string, 
  thumbnail?: string
): string {
  const embedUrl = convertToEmbedUrl(videoUrl);
  
  return `
    <div class="lesson-video">
      <h2>${escapeHtml(title)}</h2>
      <div class="video-container">
        <iframe
          src="${embedUrl}"
          frameborder="0"
          allowfullscreen
          title="${escapeHtml(title)}"
          class="video-iframe">
        </iframe>
      </div>
      ${duration ? `<p class="video-duration">Duration: ${duration}</p>` : ''}
      <div class="video-actions">
        <button onclick="markVideoComplete()" class="complete-btn">Mark Complete</button>
        <a href="${videoUrl}" target="_blank" class="external-link">Open in YouTube</a>
      </div>
    </div>
  `;
}

/**
 * Calculates total course duration from learning path
 */
export function calculateTotalDuration(learningPath: LearningPathFormat): DurationInfo {
  let totalSeconds = 0;

  Object.values(learningPath.journey).forEach(lessons => {
    lessons.forEach(lesson => {
      if (lesson.duration) {
        const duration = parseDuration(lesson.duration);
        totalSeconds += duration.totalSeconds;
      }
    });
  });

  const hours = Math.floor(totalSeconds / 3600);
  const minutes = Math.floor((totalSeconds % 3600) / 60);
  const seconds = totalSeconds % 60;

  const formatted = hours > 0 
    ? `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
    : `${minutes}:${seconds.toString().padStart(2, '0')}`;

  return { totalSeconds, formatted, hours, minutes, seconds };
}
