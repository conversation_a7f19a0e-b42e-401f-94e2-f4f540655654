// Test examples and sample data for SCORM converter

import { LearningPathFormat, SCORMFormat, ConversionOptions } from './types.ts';

// ============================================================================
// SAMPLE LEARNING PATH DATA
// ============================================================================

export const sampleLearningPath: LearningPathFormat = {
  title: "Full-Stack Development: A Comprehensive Guide",
  img_url: "https://img.freepik.com/free-photo/programming-background-with-person-working-with-codes-computer_23-2150010125.jpg",
  journey: {
    "Introduction to Web Development": [
      {
        title: "What is Full-Stack Development?",
        link: "https://www.youtube.com/watch?v=WG5ikvJ2TKA",
        type: "video",
        duration: "12:45",
        img_url: "https://img.youtube.com/vi/WG5ikvJ2TKA/maxresdefault.jpg"
      },
      {
        title: "Setting Up Your Development Environment",
        link: "https://www.youtube.com/watch?v=mI_-1tbIXQI",
        type: "video",
        duration: "18:30"
      }
    ],
    "Frontend Development": [
      {
        title: "HTML Fundamentals",
        link: "https://www.youtube.com/watch?v=UB1O30fR-EE",
        type: "video",
        duration: "25:15"
      },
      {
        title: "CSS Styling and Layout",
        link: "https://www.youtube.com/watch?v=yfoY53QXEnI",
        type: "video",
        duration: "32:20"
      },
      {
        title: "JavaScript Basics",
        link: "https://www.youtube.com/watch?v=W6NZfCO5SIk",
        type: "video",
        duration: "28:45"
      }
    ],
    "Backend Development": [
      {
        title: "Introduction to Node.js",
        link: "https://www.youtube.com/watch?v=TlB_eWDSMt4",
        type: "video",
        duration: "22:10"
      },
      {
        title: "Building REST APIs",
        link: "https://www.youtube.com/watch?v=pKd0Rpw7O48",
        type: "video",
        duration: "35:30"
      }
    ],
    "Database Integration": [
      {
        title: "SQL Database Fundamentals",
        link: "https://www.youtube.com/watch?v=HXV3zeQKqGY",
        type: "video",
        duration: "40:15"
      },
      {
        title: "Working with MongoDB",
        link: "https://www.youtube.com/watch?v=ExcRbA7fy_A",
        type: "video",
        duration: "30:45"
      }
    ]
  }
};

// ============================================================================
// SAMPLE SCORM DATA
// ============================================================================

export const sampleSCORMData: SCORMFormat = {
  manifest: {
    identifier: "course_fullstack_development",
    version: "1.0",
    metadata: {
      schema: "ADL SCORM",
      schemaversion: "2004 4th Edition",
      title: "Full-Stack Development: A Comprehensive Guide",
      description: "A comprehensive course covering frontend, backend, and database development",
      language: "en-US",
      author: "Learnify Flow Studio",
      duration: "PT4H15M"
    },
    organizations: {
      default: "org_fullstack_development",
      organizations: [
        {
          identifier: "org_fullstack_development",
          title: "Full-Stack Development: A Comprehensive Guide",
          structure: "hierarchical",
          objectivesGlobalToSystem: false,
          items: [
            {
              identifier: "module_0_introduction",
              title: "Introduction to Web Development",
              isvisible: true,
              children: [
                {
                  identifier: "lesson_0_0_what_is_fullstack",
                  identifierref: "resource_lesson_0",
                  title: "What is Full-Stack Development?",
                  isvisible: true,
                  timelimitaction: "continue,no message"
                },
                {
                  identifier: "lesson_0_1_setup_environment",
                  identifierref: "resource_lesson_1",
                  title: "Setting Up Your Development Environment",
                  isvisible: true,
                  timelimitaction: "continue,no message"
                }
              ]
            },
            {
              identifier: "module_1_frontend",
              title: "Frontend Development",
              isvisible: true,
              children: [
                {
                  identifier: "lesson_1_0_html_fundamentals",
                  identifierref: "resource_lesson_2",
                  title: "HTML Fundamentals",
                  isvisible: true,
                  timelimitaction: "continue,no message"
                }
              ]
            }
          ]
        }
      ]
    },
    resources: [
      {
        identifier: "resource_main_0",
        type: "webcontent",
        href: "index.html",
        scormType: "sco",
        files: ["index.html", "styles.css", "scorm-api.js"],
        metadata: {
          title: "Full-Stack Development: A Comprehensive Guide",
          description: "Main course content"
        }
      },
      {
        identifier: "resource_lesson_0",
        type: "webcontent",
        href: "lessons/lesson_0_0.html",
        scormType: "sco",
        files: ["lessons/lesson_0_0.html"],
        metadata: {
          title: "What is Full-Stack Development?",
          duration: "12:45"
        }
      },
      {
        identifier: "resource_lesson_1",
        type: "webcontent",
        href: "lessons/lesson_0_1.html",
        scormType: "sco",
        files: ["lessons/lesson_0_1.html"],
        metadata: {
          title: "Setting Up Your Development Environment",
          duration: "18:30"
        }
      }
    ]
  },
  contentFiles: {
    "index.html": `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Full-Stack Development Course</title>
    <link rel="stylesheet" href="styles.css">
    <script src="scorm-api.js"></script>
</head>
<body>
    <div class="course-container">
        <h1>Full-Stack Development: A Comprehensive Guide</h1>
        <nav class="course-navigation">
            <h2>Course Content</h2>
            <div class="module-nav">
                <h3>Introduction to Web Development</h3>
                <ul>
                    <li><a href="lessons/lesson_0_0.html">What is Full-Stack Development?</a></li>
                    <li><a href="lessons/lesson_0_1.html">Setting Up Your Development Environment</a></li>
                </ul>
            </div>
        </nav>
    </div>
</body>
</html>`,
    "styles.css": `body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }
.course-container { max-width: 1200px; margin: 0 auto; }
.course-navigation { background: #f5f5f5; padding: 20px; border-radius: 8px; }
.module-nav { margin-bottom: 20px; }
.module-nav h3 { color: #333; border-bottom: 2px solid #007cba; padding-bottom: 5px; }
.module-nav ul { list-style: none; padding: 0; }
.module-nav li { margin: 8px 0; }
.module-nav a { color: #007cba; text-decoration: none; }`,
    "lessons/lesson_0_0.html": `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>What is Full-Stack Development?</title>
    <link rel="stylesheet" href="../styles.css">
    <script src="../scorm-api.js"></script>
</head>
<body>
    <div class="lesson-container">
        <h1>What is Full-Stack Development?</h1>
        <div class="video-container">
            <iframe src="https://www.youtube.com/embed/WG5ikvJ2TKA" frameborder="0" allowfullscreen></iframe>
        </div>
        <p>Duration: 12:45</p>
        <button onclick="markLessonComplete('lesson_0_0')">Mark Complete</button>
    </div>
</body>
</html>`,
    "scorm-api.js": `var scormAPI = {
    initialized: false,
    initialize: function() { this.initialized = true; return 'true'; },
    setStatus: function(status) { localStorage.setItem('scorm_status', status); return 'true'; },
    commit: function() { return 'true'; },
    terminate: function() { this.initialized = false; return 'true'; }
};`
  }
};

// ============================================================================
// CONVERSION OPTIONS EXAMPLES
// ============================================================================

export const defaultConversionOptions: ConversionOptions = {
  scormVersion: '2004',
  includeVideos: true,
  generateUniqueIds: true,
  preserveStructure: true,
  language: 'en-US',
  author: 'Learnify Flow Studio'
};

export const scorm12Options: ConversionOptions = {
  scormVersion: '1.2',
  includeVideos: true,
  packageTitle: 'SCORM 1.2 Package',
  packageDescription: 'Legacy SCORM 1.2 compatible package',
  generateUniqueIds: true
};

export const minimalOptions: ConversionOptions = {
  scormVersion: '2004',
  includeVideos: false
};

// ============================================================================
// TEST FUNCTIONS
// ============================================================================

/**
 * Test learning path to SCORM conversion
 */
export async function testToSCORMConversion(): Promise<void> {
  console.log('Testing Learning Path to SCORM conversion...');
  
  const requestBody = {
    type: 'to-scorm',
    data: sampleLearningPath,
    options: defaultConversionOptions
  };

  try {
    const response = await fetch('/functions/v1/scorm-converter', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(requestBody)
    });

    const result = await response.json();
    
    if (result.success) {
      console.log('✅ Conversion successful');
      console.log('Generated files:', Object.keys(result.data.contentFiles));
      console.log('Manifest identifier:', result.data.manifest.identifier);
    } else {
      console.log('❌ Conversion failed:', result.error);
    }
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

/**
 * Test SCORM to learning path conversion
 */
export async function testFromSCORMConversion(): Promise<void> {
  console.log('Testing SCORM to Learning Path conversion...');
  
  const requestBody = {
    type: 'from-scorm',
    data: sampleSCORMData,
    options: { preserveStructure: true }
  };

  try {
    const response = await fetch('/functions/v1/scorm-converter', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(requestBody)
    });

    const result = await response.json();
    
    if (result.success) {
      console.log('✅ Conversion successful');
      console.log('Course title:', result.data.title);
      console.log('Module count:', Object.keys(result.data.journey).length);
    } else {
      console.log('❌ Conversion failed:', result.error);
    }
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

/**
 * Test error handling with invalid data
 */
export async function testErrorHandling(): Promise<void> {
  console.log('Testing error handling...');
  
  const invalidRequestBody = {
    type: 'invalid-type',
    data: null
  };

  try {
    const response = await fetch('/functions/v1/scorm-converter', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(invalidRequestBody)
    });

    const result = await response.json();
    
    if (!result.success) {
      console.log('✅ Error handling working correctly');
      console.log('Error code:', result.code);
      console.log('Error message:', result.error);
    } else {
      console.log('❌ Error handling failed - should have returned error');
    }
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

/**
 * Run all tests
 */
export async function runAllTests(): Promise<void> {
  console.log('🧪 Running SCORM Converter Tests...\n');
  
  await testToSCORMConversion();
  console.log('');
  
  await testFromSCORMConversion();
  console.log('');
  
  await testErrorHandling();
  console.log('');
  
  console.log('✅ All tests completed');
}
