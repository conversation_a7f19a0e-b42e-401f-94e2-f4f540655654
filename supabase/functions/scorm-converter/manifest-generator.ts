// SCORM Manifest XML Generation Utilities

import {
  SCORMManifest,
  SCORMOrganization,
  SCORMItem,
  SCORMResource,
  ConversionOptions
} from './types.ts';

import {
  escapeXml,
  wrapInXmlTag,
  generateXmlAttributes
} from './utils.ts';

/**
 * Generates complete SCORM manifest XML from manifest object
 */
export function generateManifestXML(manifest: SCORMManifest, options: ConversionOptions = {}): string {
  const scormVersion = options.scormVersion || '2004';
  
  // Generate XML declaration
  const xmlDeclaration = '<?xml version="1.0" encoding="UTF-8"?>';
  
  // Generate manifest opening tag with namespaces
  const manifestAttributes = {
    identifier: manifest.identifier,
    version: manifest.version,
    xmlns: 'http://www.imsglobal.org/xsd/imscp_v1p1',
    'xmlns:adlcp': 'http://www.adlnet.org/xsd/adlcp_v1p3',
    'xmlns:adlseq': 'http://www.adlnet.org/xsd/adlseq_v1p3',
    'xmlns:adlnav': 'http://www.adlnet.org/xsd/adlnav_v1p3',
    'xmlns:imsss': 'http://www.imsglobal.org/xsd/imsss',
    'xmlns:xsi': 'http://www.w3.org/2001/XMLSchema-instance',
    'xmlns:lom': 'http://ltsc.ieee.org/xsd/LOM'
  };

  if (scormVersion === '2004') {
    manifestAttributes['xsi:schemaLocation'] = [
      'http://www.imsglobal.org/xsd/imscp_v1p1 imscp_v1p1.xsd',
      'http://www.adlnet.org/xsd/adlcp_v1p3 adlcp_v1p3.xsd',
      'http://www.adlnet.org/xsd/adlseq_v1p3 adlseq_v1p3.xsd',
      'http://www.adlnet.org/xsd/adlnav_v1p3 adlnav_v1p3.xsd',
      'http://www.imsglobal.org/xsd/imsss imsss_v1p0.xsd'
    ].join(' ');
  }

  // Generate manifest content
  const metadataXML = generateMetadataXML(manifest.metadata);
  const organizationsXML = generateOrganizationsXML(manifest.organizations);
  const resourcesXML = generateResourcesXML(manifest.resources);

  const manifestContent = [
    metadataXML,
    organizationsXML,
    resourcesXML
  ].join('\n  ');

  const manifestXML = wrapInXmlTag('manifest', `\n  ${manifestContent}\n`, manifestAttributes);

  return `${xmlDeclaration}\n${manifestXML}`;
}

/**
 * Generates metadata XML section
 */
function generateMetadataXML(metadata: any): string {
  const schemaXML = wrapInXmlTag('schema', metadata.schema);
  const schemaversionXML = wrapInXmlTag('schemaversion', metadata.schemaversion);
  
  const lomTitleXML = wrapInXmlTag('lom:string', escapeXml(metadata.title), { language: 'en-US' });
  const lomTitle = wrapInXmlTag('lom:title', `\n      ${lomTitleXML}\n    `);
  
  const lomDescriptionXML = metadata.description 
    ? wrapInXmlTag('lom:string', escapeXml(metadata.description), { language: 'en-US' })
    : '';
  const lomDescription = lomDescriptionXML 
    ? wrapInXmlTag('lom:description', `\n      ${lomDescriptionXML}\n    `)
    : '';

  const lomGeneralContent = [lomTitle, lomDescription].filter(Boolean).join('\n    ');
  const lomGeneral = wrapInXmlTag('lom:general', `\n    ${lomGeneralContent}\n  `);
  const lomLom = wrapInXmlTag('lom:lom', `\n  ${lomGeneral}\n`);

  const metadataContent = [schemaXML, schemaversionXML, lomLom].join('\n    ');
  
  return wrapInXmlTag('metadata', `\n    ${metadataContent}\n  `);
}

/**
 * Generates organizations XML section
 */
function generateOrganizationsXML(organizations: any): string {
  const organizationsArray = organizations.organizations || [];
  
  const organizationXMLs = organizationsArray.map((org: SCORMOrganization) => 
    generateOrganizationXML(org)
  );

  const organizationsContent = organizationXMLs.join('\n    ');
  
  return wrapInXmlTag('organizations', `\n    ${organizationsContent}\n  `, {
    default: organizations.default
  });
}

/**
 * Generates single organization XML
 */
function generateOrganizationXML(organization: SCORMOrganization): string {
  const titleXML = wrapInXmlTag('title', escapeXml(organization.title));
  
  const itemXMLs = organization.items.map(item => generateItemXML(item, 2));
  const itemsContent = itemXMLs.join('\n      ');

  const organizationContent = [titleXML, itemsContent].join('\n      ');

  const attributes: any = {
    identifier: organization.identifier
  };

  if (organization.structure) {
    attributes.structure = organization.structure;
  }

  if (organization.objectivesGlobalToSystem !== undefined) {
    attributes['adlseq:objectivesGlobalToSystem'] = organization.objectivesGlobalToSystem.toString();
  }

  return wrapInXmlTag('organization', `\n      ${organizationContent}\n    `, attributes);
}

/**
 * Generates item XML recursively
 */
function generateItemXML(item: SCORMItem, indentLevel: number = 0): string {
  const indent = '  '.repeat(indentLevel);
  const childIndent = '  '.repeat(indentLevel + 1);

  const titleXML = wrapInXmlTag('title', escapeXml(item.title));
  
  let content = titleXML;

  // Add child items if they exist
  if (item.children && item.children.length > 0) {
    const childXMLs = item.children.map(child => 
      generateItemXML(child, indentLevel + 2)
    );
    const childrenContent = childXMLs.join(`\n${childIndent}  `);
    content += `\n${childIndent}  ${childrenContent}`;
  }

  const attributes: any = {
    identifier: item.identifier
  };

  if (item.identifierref) {
    attributes.identifierref = item.identifierref;
  }

  if (item.isvisible !== undefined) {
    attributes.isvisible = item.isvisible.toString();
  }

  if (item.parameters) {
    attributes.parameters = item.parameters;
  }

  if (item.timelimitaction) {
    attributes.timelimitaction = item.timelimitaction;
  }

  if (item.datafromlms) {
    attributes.datafromlms = item.datafromlms;
  }

  if (item.mastery_score !== undefined) {
    attributes.mastery_score = item.mastery_score.toString();
  }

  if (item.max_time_allowed) {
    attributes.max_time_allowed = item.max_time_allowed;
  }

  return wrapInXmlTag('item', `\n${childIndent}${content}\n${indent}`, attributes);
}

/**
 * Generates resources XML section
 */
function generateResourcesXML(resources: SCORMResource[]): string {
  const resourceXMLs = resources.map(resource => generateResourceXML(resource));
  const resourcesContent = resourceXMLs.join('\n    ');
  
  return wrapInXmlTag('resources', `\n    ${resourcesContent}\n  `);
}

/**
 * Generates single resource XML
 */
function generateResourceXML(resource: SCORMResource): string {
  const fileXMLs = resource.files.map(filename => 
    wrapInXmlTag('file', '', { href: filename })
  );
  
  const filesContent = fileXMLs.join('\n      ');

  const attributes: any = {
    identifier: resource.identifier,
    type: resource.type,
    href: resource.href
  };

  if (resource.scormType) {
    attributes['adlcp:scormType'] = resource.scormType;
  }

  return wrapInXmlTag('resource', `\n      ${filesContent}\n    `, attributes);
}

/**
 * Generates imsmanifest.xml file content
 */
export function generateIMSManifest(manifest: SCORMManifest, options: ConversionOptions = {}): string {
  return generateManifestXML(manifest, options);
}

/**
 * Validates manifest structure before XML generation
 */
export function validateManifestForXML(manifest: SCORMManifest): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (!manifest.identifier) {
    errors.push('Manifest identifier is required');
  }

  if (!manifest.version) {
    errors.push('Manifest version is required');
  }

  if (!manifest.metadata) {
    errors.push('Manifest metadata is required');
  } else {
    if (!manifest.metadata.title) {
      errors.push('Manifest metadata title is required');
    }
    if (!manifest.metadata.schema) {
      errors.push('Manifest metadata schema is required');
    }
    if (!manifest.metadata.schemaversion) {
      errors.push('Manifest metadata schemaversion is required');
    }
  }

  if (!manifest.organizations) {
    errors.push('Manifest organizations are required');
  } else {
    if (!manifest.organizations.default) {
      errors.push('Default organization identifier is required');
    }
    if (!manifest.organizations.organizations || manifest.organizations.organizations.length === 0) {
      errors.push('At least one organization is required');
    }
  }

  if (!manifest.resources || manifest.resources.length === 0) {
    errors.push('At least one resource is required');
  }

  return { isValid: errors.length === 0, errors };
}

/**
 * Generates SCORM package file structure summary
 */
export function generatePackageStructure(manifest: SCORMManifest): string[] {
  const files: string[] = ['imsmanifest.xml'];

  // Add schema files for SCORM 2004
  const schemaFiles = [
    'adlcp_v1p3.xsd',
    'adlnav_v1p3.xsd',
    'adlseq_v1p3.xsd',
    'imscp_v1p1.xsd',
    'imsss_v1p0.xsd'
  ];
  files.push(...schemaFiles);

  // Add resource files
  manifest.resources.forEach(resource => {
    files.push(...resource.files);
  });

  // Remove duplicates and sort
  return [...new Set(files)].sort();
}
