// TypeScript interfaces for SCORM conversion

// ============================================================================
// LEARNING PATH FORMAT INTERFACES (Current Format)
// ============================================================================

export interface LearningPathLesson {
  title: string;
  link: string; // YouTube URL or other video link
  type: 'video' | 'text' | 'quiz' | 'interactive';
  duration: string; // Format: "MM:SS" or "HH:MM:SS"
  img_url?: string; // Optional thumbnail URL
  description?: string;
}

export interface LearningPathFormat {
  title: string;
  img_url: string;
  journey: {
    [moduleName: string]: LearningPathLesson[];
  };
}

// ============================================================================
// SCORM FORMAT INTERFACES (Target Format)
// ============================================================================

export interface SCORMResource {
  identifier: string;
  type: 'webcontent' | 'sco' | 'asset';
  href: string;
  scormType?: 'sco' | 'asset';
  files: string[];
  metadata?: {
    title?: string;
    description?: string;
    duration?: string;
  };
}

export interface SCORMItem {
  identifier: string;
  identifierref?: string;
  title: string;
  isvisible?: boolean;
  parameters?: string;
  timelimitaction?: 'exit,message' | 'exit,no message' | 'continue,message' | 'continue,no message';
  datafromlms?: string;
  mastery_score?: number;
  max_time_allowed?: string;
  children?: SCORMItem[];
}

export interface SCORMOrganization {
  identifier: string;
  title: string;
  structure?: 'hierarchical' | 'networked';
  objectivesGlobalToSystem?: boolean;
  items: SCORMItem[];
}

export interface SCORMManifest {
  identifier: string;
  version: string;
  metadata: {
    schema: string;
    schemaversion: string;
    title: string;
    description?: string;
    language?: string;
    author?: string;
    duration?: string;
  };
  organizations: {
    default: string;
    organizations: SCORMOrganization[];
  };
  resources: SCORMResource[];
}

export interface SCORMFormat {
  manifest: SCORMManifest;
  contentFiles: {
    [filename: string]: string; // filename -> content
  };
}

// ============================================================================
// CONVERSION REQUEST/RESPONSE INTERFACES
// ============================================================================

export interface ConversionRequest {
  type: 'to-scorm' | 'from-scorm';
  data: LearningPathFormat | SCORMFormat;
  options?: ConversionOptions;
}

export interface ConversionOptions {
  scormVersion?: '1.2' | '2004';
  includeVideos?: boolean;
  packageTitle?: string;
  packageDescription?: string;
  language?: string;
  author?: string;
  generateUniqueIds?: boolean;
  preserveStructure?: boolean;
}

export interface ConversionResponse {
  success: boolean;
  data?: LearningPathFormat | SCORMFormat;
  format: 'scorm' | 'learning_path';
  error?: string;
  details?: string;
  warnings?: string[];
}

// ============================================================================
// VALIDATION INTERFACES
// ============================================================================

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

export interface ValidationContext {
  format: 'learning_path' | 'scorm';
  strict?: boolean;
  allowMissingFields?: boolean;
}

// ============================================================================
// UTILITY INTERFACES
// ============================================================================

export interface YouTubeVideoInfo {
  videoId: string;
  embedUrl: string;
  watchUrl: string;
  thumbnailUrl?: string;
  title?: string;
  duration?: string;
}

export interface DurationInfo {
  totalSeconds: number;
  formatted: string; // "HH:MM:SS" or "MM:SS"
  hours: number;
  minutes: number;
  seconds: number;
}

export interface UniqueIdGenerator {
  generateCourseId(title: string): string;
  generateModuleId(moduleTitle: string, index: number): string;
  generateLessonId(lessonTitle: string, moduleIndex: number, lessonIndex: number): string;
  generateResourceId(resourceType: string, index: number): string;
}

// ============================================================================
// ERROR INTERFACES
// ============================================================================

export interface ConversionError extends Error {
  code: string;
  context?: any;
  suggestions?: string[];
}

export interface ValidationError extends Error {
  field: string;
  value: any;
  expectedType: string;
  context?: string;
}

// ============================================================================
// CONTENT TYPE INTERFACES
// ============================================================================

export interface ContentBlock {
  id: string;
  type: 'text' | 'heading' | 'video' | 'quiz' | 'list' | 'divider' | 'embed';
  order: number;
  content: any;
  metadata?: {
    duration?: string;
    url?: string;
    title?: string;
  };
}

export interface QuizContent {
  question: string;
  options: string[];
  correctAnswer: number;
  explanation?: string;
  points?: number;
}

export interface VideoContent {
  url: string;
  title: string;
  duration?: string;
  thumbnail?: string;
  embedUrl?: string;
}

// ============================================================================
// SCORM SPECIFIC INTERFACES
// ============================================================================

export interface SCORMSequencingRules {
  controlMode?: {
    choice?: boolean;
    choiceExit?: boolean;
    flow?: boolean;
    forwardOnly?: boolean;
  };
  limitConditions?: {
    attemptLimit?: number;
    attemptAbsoluteDurationLimit?: string;
  };
  auxiliaryResources?: string[];
}

export interface SCORMObjective {
  objectiveID: string;
  satisfiedByMeasure?: boolean;
  minNormalizedMeasure?: number;
  primary?: boolean;
}

export interface SCORMCompletionThreshold {
  minProgressMeasure?: number;
  progressWeight?: number;
  completedByMeasure?: boolean;
}

// ============================================================================
// EXPORT ALL TYPES
// ============================================================================

export type ConversionType = 'to-scorm' | 'from-scorm';
export type SCORMVersion = '1.2' | '2004';
export type ContentType = 'video' | 'text' | 'quiz' | 'interactive';
export type ResourceType = 'webcontent' | 'sco' | 'asset';
