import { Course, Module, Lesson } from "@/types/course";
import <PERSON><PERSON><PERSON><PERSON> from "jszip";

export interface SCORMExportOptions {
  includeVideos?: boolean;
  packageTitle?: string;
  packageDescription?: string;
  version?: '1.2' | '2004';
}

class SCORMExporter {
  private generateManifest(course: Course, options: SCORMExportOptions = {}): string {
    console.log('Generating manifest for course with', course.modules.length, 'modules');
    const courseId = course.id.replace(/[^a-zA-Z0-9]/g, '_');

    // Following the reference structure: single-page SCORM package
    const manifestContent = `<?xml version="1.0" encoding="UTF-8"?>
<manifest
  identifier="${courseId}"
  version="1.0"
  xmlns="http://www.imsglobal.org/xsd/imscp_v1p1"
  xmlns:adlcp="http://www.adlnet.org/xsd/adlcp_v1p3"
  xmlns:adlseq="http://www.adlnet.org/xsd/adlseq_v1p3"
  xmlns:adlnav="http://www.adlnet.org/xsd/adlnav_v1p3"
  xmlns:imsss="http://www.imsglobal.org/xsd/imsss"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xmlns:lom="http://ltsc.ieee.org/xsd/LOM"
  xsi:schemaLocation="http://www.imsglobal.org/xsd/imscp_v1p1 imscp_v1p1.xsd
    http://www.adlnet.org/xsd/adlcp_v1p3 adlcp_v1p3.xsd
    http://www.adlnet.org/xsd/adlseq_v1p3 adlseq_v1p3.xsd
    http://www.adlnet.org/xsd/adlnav_v1p3 adlnav_v1p3.xsd
    http://www.imsglobal.org/xsd/imsss imsss_v1p0.xsd"
>
  <metadata>
    <schema>ADL SCORM</schema>
    <schemaversion>2004 4th Edition</schemaversion>
    <lom:lom>
      <lom:general>
        <lom:title>
          <lom:string language="en-US">${this.escapeXml(course.title)}</lom:string>
        </lom:title>
        <lom:description>
          <lom:string language="en-US">${this.escapeXml(course.description)}</lom:string>
        </lom:description>
      </lom:general>
    </lom:lom>
  </metadata>
  <organizations default="${courseId}_org">
    <organization identifier="${courseId}_org" adlseq:objectivesGlobalToSystem="false">
      <title>${this.escapeXml(course.title)}</title>
      <item identifier="item_${courseId}" identifierref="resource_${courseId}">
        <title>${this.escapeXml(course.title)}</title>
      </item>
    </organization>
  </organizations>
  <resources>
    <resource identifier="resource_${courseId}" type="webcontent" adlcp:scormType="sco" href="index.html">
      <file href="index.html" />
      <file href="pipwerksWrapper.js" />
      <file href="scorm.js" />
    </resource>
  </resources>
</manifest>`;

    return manifestContent;
  }

  private generateIndexHTML(course: Course): string {
    console.log('Generating single-page index.html for course with', course.modules.length, 'modules');

    // Generate course content as a single-page application
    const courseContent = this.generateCourseContent(course);

    return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${this.escapeHtml(course.title)}</title>
    <style>
        ${this.generateCourseStyles()}
    </style>
</head>
<body>
    <div id="course-container">
        <header class="course-header">
            <h1>${this.escapeHtml(course.title)}</h1>
            <p class="course-description">${this.escapeHtml(course.description)}</p>
        </header>

        <nav class="course-navigation">
            <div class="nav-modules">
                ${course.modules.map((module, moduleIndex) => `
                    <div class="nav-module" data-module="${moduleIndex}">
                        <h3>${this.escapeHtml(module.title)}</h3>
                        <ul class="nav-lessons">
                            ${module.lessons.map((lesson, lessonIndex) => `
                                <li class="nav-lesson" data-module="${moduleIndex}" data-lesson="${lessonIndex}">
                                    <a href="#lesson_${moduleIndex}_${lessonIndex}">${this.escapeHtml(lesson.title)}</a>
                                </li>
                            `).join('')}
                        </ul>
                    </div>
                `).join('')}
            </div>
        </nav>

        <main class="course-content">
            ${courseContent}
        </main>

        <div class="course-progress">
            <div class="progress-bar">
                <div class="progress-fill" id="progress-fill"></div>
            </div>
            <span class="progress-text" id="progress-text">0% Complete</span>
        </div>
    </div>

    <script src="pipwerksWrapper.js"></script>
    <script src="scorm.js"></script>
    <script>
        ${this.generateCourseScript(course)}
    </script>
</body>
</html>`;
  }

  private generateLessonHTML(lesson: Lesson, moduleIndex: number, lessonIndex: number): string {
    console.log(`    Generating HTML for lesson: "${lesson.title}"`);
    console.log(`      Content blocks: ${lesson.contentBlocks?.length || 0}`);
    console.log(`      Video URL: ${lesson.metadata?.videoUrl || 'none'}`);
    console.log(`      Duration: ${lesson.duration}`);

    const contentBlocks = lesson.contentBlocks || [];

    // Get video URL from metadata if available
    const videoUrl = lesson.metadata?.videoUrl || '';
    const videoEmbedUrl = this.convertToEmbedUrl(videoUrl);

    console.log(`      Embed URL: ${videoEmbedUrl}`);

    const htmlContent = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${this.escapeHtml(lesson.title)}</title>
    <link rel="stylesheet" href="../styles/lesson.css">
    <script src="../scripts/scorm-api.js"></script>
</head>
<body>
    <div class="lesson-container">
        <header class="lesson-header">
            <h1>${this.escapeHtml(lesson.title)}</h1>
            <div class="lesson-meta">
                <span class="duration">⏱️ Duration: ${lesson.duration}</span>
                <span class="type">📹 Type: ${lesson.type}</span>
            </div>
        </header>

        <main class="lesson-content">
            ${contentBlocks.length > 0 ?
              contentBlocks
                .sort((a, b) => (a.order || 0) - (b.order || 0))
                .map(block => this.generateContentBlockHTML(block))
                .join('\n            ')
              : this.generateFallbackContent(lesson, videoEmbedUrl)
            }
        </main>

        <footer class="lesson-footer">
            <div class="lesson-actions">
                <button onclick="completeLessonAndNext()" class="complete-btn">
                    ✅ Mark Complete & Continue
                </button>
                ${videoUrl ? `<a href="${videoUrl}" target="_blank" class="external-link-btn">
                    🔗 Open Video in New Tab
                </a>` : ''}
            </div>
        </footer>
    </div>

    <script>
        // SCORM API integration
        function completeLessonAndNext() {
            if (typeof scormAPI !== 'undefined') {
                scormAPI.setStatus('completed');
                scormAPI.setScore(100);
                scormAPI.commit();
            }

            // Show completion message
            const button = document.querySelector('.complete-btn');
            if (button) {
                button.innerHTML = '✅ Completed!';
                button.disabled = true;
                button.style.backgroundColor = '#10b981';
            }

            // Auto-advance after 2 seconds (optional)
            setTimeout(() => {
                if (window.parent && window.parent !== window) {
                    // Try to navigate to next lesson in LMS
                    window.parent.postMessage('lesson_completed', '*');
                }
            }, 2000);
        }

        // Initialize SCORM on page load
        window.onload = function() {
            if (typeof scormAPI !== 'undefined') {
                scormAPI.initialize();
                scormAPI.setStatus('incomplete');
            }

            // Add click tracking for video interactions
            const videos = document.querySelectorAll('iframe');
            videos.forEach(video => {
                video.addEventListener('load', () => {
                    console.log('Video loaded:', video.src);
                });
            });
        };

        // Handle video play events
        function trackVideoPlay(videoTitle) {
            if (typeof scormAPI !== 'undefined') {
                scormAPI.setStatus('incomplete');
                console.log('Video started:', videoTitle);
            }
        }

        // Handle quiz answers
        function checkQuizAnswer(quizName, correctAnswer) {
            const selected = document.querySelector('input[name="' + quizName + '"]:checked');
            const resultDiv = document.querySelector('.quiz-result') || document.createElement('div');
            resultDiv.className = 'quiz-result';

            if (!selected) {
                resultDiv.innerHTML = '<p class="quiz-feedback error">⚠️ Please select an answer first.</p>';
                document.querySelector('.content-quiz').appendChild(resultDiv);
                return;
            }

            const selectedValue = parseInt(selected.value);
            const isCorrect = selectedValue === correctAnswer;

            if (isCorrect) {
                resultDiv.innerHTML = '<p class="quiz-feedback success">✅ Correct! Well done.</p>';
                if (typeof scormAPI !== 'undefined') {
                    scormAPI.setScore(100);
                }
            } else {
                resultDiv.innerHTML = '<p class="quiz-feedback error">❌ Incorrect. Please review the content and try again.</p>';
                if (typeof scormAPI !== 'undefined') {
                    scormAPI.setScore(0);
                }
            }

            document.querySelector('.content-quiz').appendChild(resultDiv);

            // Disable quiz after answering
            const options = document.querySelectorAll('input[name="' + quizName + '"]');
            options.forEach(option => option.disabled = true);
            document.querySelector('.quiz-submit').disabled = true;
        }
    </script>
</body>
</html>`;

    return htmlContent;
  }

  private convertToEmbedUrl(url: string): string {
    if (!url) return '';

    // Convert YouTube watch URLs to embed URLs
    if (url.includes('youtube.com/watch?v=')) {
      const videoId = url.split('v=')[1]?.split('&')[0];
      return videoId ? `https://www.youtube.com/embed/${videoId}` : url;
    }

    if (url.includes('youtu.be/')) {
      const videoId = url.split('youtu.be/')[1]?.split('?')[0];
      return videoId ? `https://www.youtube.com/embed/${videoId}` : url;
    }

    return url;
  }

  private generateFallbackContent(lesson: Lesson, videoEmbedUrl: string): string {
    return `
      <div class="lesson-intro">
        <h2>📚 ${this.escapeHtml(lesson.title)}</h2>
        <p class="lesson-description">${this.escapeHtml(lesson.content || 'No description available.')}</p>
      </div>

      ${videoEmbedUrl ? `
        <div class="content-video">
          <h3>🎥 Video Content</h3>
          <div class="video-container">
            <iframe
              src="${videoEmbedUrl}"
              frameborder="0"
              allowfullscreen
              title="${this.escapeHtml(lesson.title)}"
              onload="trackVideoPlay('${this.escapeHtml(lesson.title)}')"
            ></iframe>
          </div>
          <p class="video-duration">⏱️ Duration: ${lesson.duration}</p>
        </div>
      ` : ''}

      <div class="lesson-objectives">
        <h3>🎯 Learning Objectives</h3>
        <ul>
          <li>Understand the key concepts covered in this lesson</li>
          <li>Apply the knowledge to real-world scenarios</li>
          <li>Complete the lesson activities and assessments</li>
        </ul>
      </div>
    `;
  }

  private generateContentBlockHTML(block: any): string {
    switch (block.type) {
      case 'heading': {
        const level = block.content.level || 'h2';
        return `<${level} class="content-heading">${this.escapeHtml(block.content.text)}</${level}>`;
      }

      case 'text': {
        return `<div class="content-text"><p>${this.escapeHtml(block.content.text)}</p></div>`;
      }

      case 'video': {
        const videoUrl = this.convertToEmbedUrl(block.content.url || '');
        return `<div class="content-video">
          <h3>🎥 ${this.escapeHtml(block.content.title || 'Video Content')}</h3>
          <div class="video-container">
            <iframe
              src="${videoUrl}"
              frameborder="0"
              allowfullscreen
              title="${this.escapeHtml(block.content.title || 'Video')}"
              onload="trackVideoPlay('${this.escapeHtml(block.content.title || 'Video')}')"
            ></iframe>
          </div>
          ${block.content.duration ? `<p class="video-duration">⏱️ Duration: ${block.content.duration}</p>` : ''}
          <div class="video-description">
            <p>Watch this video to learn about the key concepts. You can pause and replay as needed.</p>
          </div>
        </div>`;
      }

      case 'quiz': {
        return `<div class="content-quiz">
          <h3>❓ ${this.escapeHtml(block.content.question)}</h3>
          <div class="quiz-options">
            ${block.content.options.map((option: string, index: number) =>
              `<label class="quiz-option">
                <input type="radio" name="quiz_${block.id}" value="${index}">
                ${this.escapeHtml(option)}
              </label>`
            ).join('')}
          </div>
          <button onclick="checkQuizAnswer('quiz_${block.id}', ${block.content.correctAnswer || 0})" class="quiz-submit">
            Submit Answer
          </button>
        </div>`;
      }

      case 'list': {
        const listType = block.content.listType === 'numbered' ? 'ol' : 'ul';
        return `<div class="content-list">
          <h3>📋 Key Points</h3>
          <${listType}>
            ${block.content.items.map((item: string) => `<li>${this.escapeHtml(item)}</li>`).join('')}
          </${listType}>
        </div>`;
      }

      case 'quote': {
        return `<blockquote class="content-quote">
          <p>"${this.escapeHtml(block.content.text)}"</p>
          ${block.content.author ? `<cite>— ${this.escapeHtml(block.content.author)}</cite>` : ''}
        </blockquote>`;
      }

      case 'divider': {
        return `<hr class="content-divider" />`;
      }

      default: {
        return `<div class="content-unknown">
          <p>⚠️ Content type "${block.type}" not supported in SCORM export</p>
          <details>
            <summary>Debug Info</summary>
            <pre>${JSON.stringify(block, null, 2)}</pre>
          </details>
        </div>`;
      }
    }
  }

  private generateCSS(): string {
    return `/* SCORM Lesson Styles */
body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #333;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    background-color: #f8fafc;
}

.lesson-container {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    overflow: hidden;
}

.lesson-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 30px;
    text-align: center;
}

.lesson-header h1 {
    margin: 0 0 10px 0;
    font-size: 2rem;
}

.lesson-meta {
    display: flex;
    justify-content: center;
    gap: 20px;
    font-size: 0.9rem;
    opacity: 0.9;
}

.lesson-content {
    padding: 30px;
}

.content-heading {
    color: #2d3748;
    margin: 20px 0 15px 0;
}

.content-text {
    margin: 15px 0;
}

.content-video {
    margin: 20px 0;
    text-align: center;
}

.video-container {
    position: relative;
    width: 100%;
    height: 0;
    padding-bottom: 56.25%; /* 16:9 aspect ratio */
    margin: 15px 0;
}

.video-container iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 8px;
}

.content-quiz {
    background: #f7fafc;
    padding: 20px;
    border-radius: 8px;
    margin: 20px 0;
}

.quiz-options {
    margin-top: 15px;
}

.quiz-option {
    display: block;
    margin: 10px 0;
    cursor: pointer;
}

.quiz-option input {
    margin-right: 10px;
}

.content-list ul, .content-list ol {
    margin: 15px 0;
    padding-left: 30px;
}

.content-quote {
    background: #edf2f7;
    border-left: 4px solid #667eea;
    padding: 20px;
    margin: 20px 0;
    font-style: italic;
}

.content-quote cite {
    display: block;
    margin-top: 10px;
    font-style: normal;
    font-weight: bold;
    color: #4a5568;
}

.lesson-footer {
    background: #f7fafc;
    padding: 20px 30px;
    border-top: 1px solid #e2e8f0;
}

.lesson-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
}

.complete-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 6px;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.2s;
    font-weight: 600;
}

.complete-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.complete-btn:disabled {
    background: #10b981;
    cursor: not-allowed;
    transform: none;
}

.external-link-btn {
    background: #f8fafc;
    color: #4a5568;
    border: 2px solid #e2e8f0;
    padding: 10px 20px;
    border-radius: 6px;
    text-decoration: none;
    font-size: 0.9rem;
    transition: all 0.2s;
    display: inline-flex;
    align-items: center;
    gap: 5px;
}

.external-link-btn:hover {
    background: #e2e8f0;
    border-color: #cbd5e0;
    transform: translateY(-1px);
}

.video-description {
    margin-top: 15px;
    padding: 15px;
    background: #f0f9ff;
    border-left: 4px solid #3b82f6;
    border-radius: 4px;
}

.video-description p {
    margin: 0;
    color: #1e40af;
    font-style: italic;
}

.lesson-intro {
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    padding: 25px;
    border-radius: 8px;
    margin-bottom: 25px;
    border-left: 4px solid #3b82f6;
}

.lesson-objectives {
    background: #f8fafc;
    padding: 20px;
    border-radius: 8px;
    margin-top: 25px;
    border: 1px solid #e2e8f0;
}

.lesson-objectives h3 {
    margin-top: 0;
    color: #2d3748;
}

.lesson-objectives ul {
    margin-bottom: 0;
}

.lesson-objectives li {
    margin-bottom: 8px;
    color: #4a5568;
}

.quiz-submit {
    background: #3b82f6;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    cursor: pointer;
    margin-top: 15px;
    transition: background 0.2s;
}

.quiz-submit:hover {
    background: #2563eb;
}

.quiz-submit:disabled {
    background: #9ca3af;
    cursor: not-allowed;
}

.quiz-result {
    margin-top: 15px;
    padding: 15px;
    border-radius: 6px;
}

.quiz-feedback.success {
    background: #d1fae5;
    color: #065f46;
    border: 1px solid #a7f3d0;
    margin: 0;
    padding: 10px;
    border-radius: 4px;
}

.quiz-feedback.error {
    background: #fee2e2;
    color: #991b1b;
    border: 1px solid #fca5a5;
    margin: 0;
    padding: 10px;
    border-radius: 4px;
}

.content-divider {
    border: none;
    height: 2px;
    background: linear-gradient(90deg, transparent, #e2e8f0, transparent);
    margin: 30px 0;
}

.content-unknown {
    background: #fed7d7;
    color: #c53030;
    padding: 10px;
    border-radius: 4px;
    margin: 10px 0;
}`;
  }

  private generateSCORMAPI(): string {
    return `// Basic SCORM API implementation
var scormAPI = {
    initialized: false,
    
    initialize: function() {
        this.initialized = true;
        console.log('SCORM API initialized');
        return 'true';
    },
    
    setStatus: function(status) {
        if (!this.initialized) return 'false';
        localStorage.setItem('scorm_status', status);
        console.log('SCORM status set to:', status);
        return 'true';
    },
    
    getStatus: function() {
        if (!this.initialized) return 'false';
        return localStorage.getItem('scorm_status') || 'not attempted';
    },
    
    setScore: function(score) {
        if (!this.initialized) return 'false';
        localStorage.setItem('scorm_score', score.toString());
        console.log('SCORM score set to:', score);
        return 'true';
    },
    
    getScore: function() {
        if (!this.initialized) return '0';
        return localStorage.getItem('scorm_score') || '0';
    },
    
    commit: function() {
        if (!this.initialized) return 'false';
        console.log('SCORM data committed');
        return 'true';
    },
    
    terminate: function() {
        this.initialized = false;
        console.log('SCORM API terminated');
        return 'true';
    }
};

// Make API available globally
window.scormAPI = scormAPI;`;
  }

  private generateCourseContent(course: Course): string {
    console.log('Generating course content for', course.modules.length, 'modules');

    return course.modules.map((module, moduleIndex) => `
      <section class="module" id="module_${moduleIndex}" data-module="${moduleIndex}">
        <div class="module-header">
          <h2>${this.escapeHtml(module.title)}</h2>
          <p class="module-description">${this.escapeHtml(module.description)}</p>
          <div class="module-meta">
            <span class="duration">${this.escapeHtml(module.estimatedDuration)}</span>
            <span class="lesson-count">${module.lessons.length} lessons</span>
          </div>
        </div>

        <div class="module-lessons">
          ${module.lessons.map((lesson, lessonIndex) => `
            <article class="lesson" id="lesson_${moduleIndex}_${lessonIndex}" data-module="${moduleIndex}" data-lesson="${lessonIndex}">
              <div class="lesson-header">
                <h3>${this.escapeHtml(lesson.title)}</h3>
                <div class="lesson-meta">
                  <span class="lesson-type">${lesson.type}</span>
                  <span class="lesson-duration">${this.escapeHtml(lesson.duration)}</span>
                </div>
              </div>

              <div class="lesson-content">
                ${this.generateLessonContentHTML(lesson)}
              </div>

              <div class="lesson-actions">
                <button class="btn-complete" onclick="markLessonComplete(${moduleIndex}, ${lessonIndex})">
                  Mark Complete
                </button>
                ${lessonIndex < module.lessons.length - 1 ?
                  `<button class="btn-next" onclick="goToLesson(${moduleIndex}, ${lessonIndex + 1})">Next Lesson</button>` :
                  moduleIndex < course.modules.length - 1 ?
                    `<button class="btn-next" onclick="goToLesson(${moduleIndex + 1}, 0)">Next Module</button>` :
                    `<button class="btn-finish" onclick="finishCourse()">Finish Course</button>`
                }
              </div>
            </article>
          `).join('')}
        </div>
      </section>
    `).join('');
  }

  private generateLessonContentHTML(lesson: Lesson): string {
    let contentHTML = '';

    // Add video content if it's a video lesson
    if (lesson.type === 'video' && lesson.metadata?.videoUrl) {
      const videoUrl = lesson.metadata.videoUrl;
      const videoId = this.extractYouTubeId(videoUrl);

      if (videoId) {
        contentHTML += `
          <div class="video-container">
            <iframe
              src="https://www.youtube.com/embed/${videoId}"
              frameborder="0"
              allowfullscreen
              class="lesson-video">
            </iframe>
          </div>
        `;
      }
    }

    // Add content blocks
    if (lesson.contentBlocks && lesson.contentBlocks.length > 0) {
      contentHTML += lesson.contentBlocks
        .sort((a, b) => a.order - b.order)
        .map(block => this.generateContentBlockHTML(block))
        .join('');
    } else {
      // Fallback to basic content
      contentHTML += `<div class="lesson-text">${this.escapeHtml(lesson.content || '')}</div>`;
    }

    return contentHTML;
  }

  private extractYouTubeId(url: string): string | null {
    const regex = /(?:youtube\.com\/watch\?v=|youtu\.be\/)([^&\n?#]+)/;
    const match = url.match(regex);
    return match ? match[1] : null;
  }

  private generateCourseStyles(): string {
    return `
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        line-height: 1.6;
        color: #333;
        background: #f8fafc;
      }

      #course-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
      }

      .course-header {
        background: white;
        padding: 30px;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin-bottom: 30px;
        text-align: center;
      }

      .course-header h1 {
        font-size: 2.5rem;
        color: #1a202c;
        margin-bottom: 15px;
      }

      .course-description {
        font-size: 1.1rem;
        color: #4a5568;
        max-width: 600px;
        margin: 0 auto;
      }

      .course-navigation {
        background: white;
        padding: 20px;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin-bottom: 30px;
      }

      .nav-modules {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
      }

      .nav-module h3 {
        color: #2d3748;
        margin-bottom: 10px;
        padding-bottom: 8px;
        border-bottom: 2px solid #e2e8f0;
      }

      .nav-lessons {
        list-style: none;
      }

      .nav-lesson {
        margin-bottom: 8px;
      }

      .nav-lesson a {
        color: #4299e1;
        text-decoration: none;
        padding: 8px 12px;
        display: block;
        border-radius: 6px;
        transition: background 0.2s;
      }

      .nav-lesson a:hover {
        background: #ebf8ff;
      }

      .course-content {
        margin-bottom: 30px;
      }

      .module {
        background: white;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin-bottom: 30px;
        overflow: hidden;
      }

      .module-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 30px;
      }

      .module-header h2 {
        font-size: 1.8rem;
        margin-bottom: 10px;
      }

      .module-description {
        font-size: 1.1rem;
        opacity: 0.9;
        margin-bottom: 15px;
      }

      .module-meta {
        display: flex;
        gap: 20px;
        font-size: 0.9rem;
        opacity: 0.8;
      }

      .lesson {
        border-bottom: 1px solid #e2e8f0;
        padding: 30px;
      }

      .lesson:last-child {
        border-bottom: none;
      }

      .lesson-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
      }

      .lesson-header h3 {
        color: #2d3748;
        font-size: 1.4rem;
      }

      .lesson-meta {
        display: flex;
        gap: 15px;
        font-size: 0.9rem;
        color: #718096;
      }

      .lesson-type {
        background: #e2e8f0;
        padding: 4px 8px;
        border-radius: 4px;
        text-transform: capitalize;
      }

      .video-container {
        position: relative;
        width: 100%;
        height: 0;
        padding-bottom: 56.25%;
        margin: 20px 0;
      }

      .lesson-video {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        border-radius: 8px;
      }

      .lesson-content {
        margin-bottom: 25px;
      }

      .lesson-text {
        font-size: 1.1rem;
        line-height: 1.7;
        color: #4a5568;
      }

      .lesson-actions {
        display: flex;
        gap: 15px;
        justify-content: flex-end;
      }

      .btn-complete, .btn-next, .btn-finish {
        padding: 12px 24px;
        border: none;
        border-radius: 6px;
        cursor: pointer;
        font-weight: 500;
        transition: all 0.2s;
      }

      .btn-complete {
        background: #48bb78;
        color: white;
      }

      .btn-complete:hover {
        background: #38a169;
      }

      .btn-next, .btn-finish {
        background: #4299e1;
        color: white;
      }

      .btn-next:hover, .btn-finish:hover {
        background: #3182ce;
      }

      .course-progress {
        background: white;
        padding: 20px;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        position: sticky;
        bottom: 20px;
      }

      .progress-bar {
        width: 100%;
        height: 8px;
        background: #e2e8f0;
        border-radius: 4px;
        overflow: hidden;
        margin-bottom: 10px;
      }

      .progress-fill {
        height: 100%;
        background: linear-gradient(90deg, #48bb78, #38a169);
        width: 0%;
        transition: width 0.3s ease;
      }

      .progress-text {
        font-weight: 500;
        color: #4a5568;
      }
    `;
  }

  private generateCourseScript(course: Course): string {
    const totalLessons = course.modules.reduce((total, module) => total + module.lessons.length, 0);

    return `
      let completedLessons = new Set();
      let currentLesson = { module: 0, lesson: 0 };
      const totalLessons = ${totalLessons};

      // Initialize SCORM
      function initializeSCORM() {
        if (typeof pipwerks !== 'undefined' && pipwerks.SCORM) {
          pipwerks.SCORM.init();

          // Get existing progress
          const suspendData = pipwerks.SCORM.get('cmi.suspend_data');
          if (suspendData) {
            try {
              const progress = JSON.parse(suspendData);
              completedLessons = new Set(progress.completed || []);
              currentLesson = progress.current || { module: 0, lesson: 0 };
            } catch (e) {
              console.warn('Could not parse suspend data:', e);
            }
          }

          updateProgress();
          goToLesson(currentLesson.module, currentLesson.lesson);
        }
      }

      function markLessonComplete(moduleIndex, lessonIndex) {
        const lessonId = moduleIndex + '_' + lessonIndex;
        completedLessons.add(lessonId);

        // Update SCORM
        if (typeof pipwerks !== 'undefined' && pipwerks.SCORM) {
          const progress = {
            completed: Array.from(completedLessons),
            current: currentLesson
          };
          pipwerks.SCORM.set('cmi.suspend_data', JSON.stringify(progress));
          pipwerks.SCORM.set('cmi.completion_status', 'incomplete');

          if (completedLessons.size === totalLessons) {
            pipwerks.SCORM.set('cmi.completion_status', 'completed');
            pipwerks.SCORM.set('cmi.success_status', 'passed');
          }

          pipwerks.SCORM.save();
        }

        updateProgress();

        // Visual feedback
        const lessonElement = document.getElementById('lesson_' + moduleIndex + '_' + lessonIndex);
        if (lessonElement) {
          lessonElement.style.opacity = '0.7';
          lessonElement.style.background = '#f0fff4';
        }
      }

      function goToLesson(moduleIndex, lessonIndex) {
        currentLesson = { module: moduleIndex, lesson: lessonIndex };

        // Hide all lessons
        document.querySelectorAll('.lesson').forEach(lesson => {
          lesson.style.display = 'none';
        });

        // Show target lesson
        const targetLesson = document.getElementById('lesson_' + moduleIndex + '_' + lessonIndex);
        if (targetLesson) {
          targetLesson.style.display = 'block';
          targetLesson.scrollIntoView({ behavior: 'smooth' });
        }

        // Update navigation
        document.querySelectorAll('.nav-lesson a').forEach(link => {
          link.classList.remove('active');
        });

        const activeLink = document.querySelector('[href="#lesson_' + moduleIndex + '_' + lessonIndex + '"]');
        if (activeLink) {
          activeLink.classList.add('active');
        }
      }

      function updateProgress() {
        const progressPercent = Math.round((completedLessons.size / totalLessons) * 100);

        const progressFill = document.getElementById('progress-fill');
        const progressText = document.getElementById('progress-text');

        if (progressFill) {
          progressFill.style.width = progressPercent + '%';
        }

        if (progressText) {
          progressText.textContent = progressPercent + '% Complete (' + completedLessons.size + '/' + totalLessons + ')';
        }
      }

      function finishCourse() {
        if (typeof pipwerks !== 'undefined' && pipwerks.SCORM) {
          pipwerks.SCORM.set('cmi.completion_status', 'completed');
          pipwerks.SCORM.set('cmi.success_status', 'passed');
          pipwerks.SCORM.save();
        }

        alert('Congratulations! You have completed the course.');
      }

      // Initialize when page loads
      document.addEventListener('DOMContentLoaded', function() {
        initializeSCORM();

        // Show first lesson by default
        goToLesson(0, 0);
      });

      // Handle page unload
      window.addEventListener('beforeunload', function() {
        if (typeof pipwerks !== 'undefined' && pipwerks.SCORM) {
          const progress = {
            completed: Array.from(completedLessons),
            current: currentLesson
          };
          pipwerks.SCORM.set('cmi.suspend_data', JSON.stringify(progress));
          pipwerks.SCORM.save();
          pipwerks.SCORM.quit();
        }
      });
    `;
  }

  private escapeXml(text: string): string {
    return text
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&apos;');
  }

  private escapeHtml(text: string): string {
    return text
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#39;');
  }

  private async addSCORMSchemaFiles(zip: JSZip): Promise<void> {
    // Add SCORM 2004 4th Edition schema files (following reference structure)
    const schemaFiles = [
      'adlcp_v1p3.xsd',
      'adlnav_v1p3.xsd',
      'adlseq_v1p3.xsd',
      'imscp_v1p1.xsd',
      'imsss_v1p0.xsd'
    ];

    // For now, add placeholder schema files
    // In a production environment, you would include the actual XSD files
    schemaFiles.forEach(fileName => {
      zip.file(fileName, `<!-- ${fileName} schema file -->`);
    });
  }

  private async addSCORMJavaScriptFiles(zip: JSZip): Promise<void> {
    // Add pipwerksWrapper.js (SCORM API wrapper)
    const pipwerksWrapper = await this.getPipwerksWrapper();
    zip.file('pipwerksWrapper.js', pipwerksWrapper);

    // Add scorm.js (SCORM implementation)
    const scormJS = await this.getSCORMJS();
    zip.file('scorm.js', scormJS);
  }

  private async getPipwerksWrapper(): Promise<string> {
    // Return the pipwerks SCORM wrapper content
    // This is a simplified version - in production you'd include the full wrapper
    return `/* pipwerks SCORM Wrapper for JavaScript */
var pipwerks = {};
pipwerks.UTILS = {};
pipwerks.debug = { isActive: false };

pipwerks.SCORM = {
    version: null,
    handleCompletionStatus: true,
    handleExitMode: true,
    API: { handle: null, isFound: false },
    connection: { isActive: false },
    data: { completionStatus: null, exitStatus: null },

    init: function() {
        console.log('SCORM initialized');
        this.connection.isActive = true;
        return true;
    },

    get: function(parameter) {
        console.log('SCORM get:', parameter);
        return '';
    },

    set: function(parameter, value) {
        console.log('SCORM set:', parameter, value);
        return true;
    },

    save: function() {
        console.log('SCORM save');
        return true;
    },

    quit: function() {
        console.log('SCORM quit');
        this.connection.isActive = false;
        return true;
    }
};`;
  }

  private async getSCORMJS(): Promise<string> {
    // Return the SCORM implementation content
    return `/* SCORM Implementation */
(function() {
    'use strict';

    var startTimeStamp, scormUnload, actions, student;

    function initializeSCORM() {
        if (typeof pipwerks !== 'undefined' && pipwerks.SCORM) {
            return pipwerks.SCORM.init();
        }
        return false;
    }

    function setSCORMValue(parameter, value) {
        if (typeof pipwerks !== 'undefined' && pipwerks.SCORM) {
            return pipwerks.SCORM.set(parameter, value);
        }
        return false;
    }

    function getSCORMValue(parameter) {
        if (typeof pipwerks !== 'undefined' && pipwerks.SCORM) {
            return pipwerks.SCORM.get(parameter);
        }
        return '';
    }

    function saveSCORMData() {
        if (typeof pipwerks !== 'undefined' && pipwerks.SCORM) {
            return pipwerks.SCORM.save();
        }
        return false;
    }

    function quitSCORM() {
        if (typeof pipwerks !== 'undefined' && pipwerks.SCORM) {
            return pipwerks.SCORM.quit();
        }
        return false;
    }

    // Initialize SCORM when page loads
    window.addEventListener('load', function() {
        initializeSCORM();
    });

    // Save data before page unloads
    window.addEventListener('beforeunload', function() {
        saveSCORMData();
        quitSCORM();
    });

})();`;
  }

  async exportCourse(course: Course, options: SCORMExportOptions = {}): Promise<Blob> {
    console.log('Starting SCORM export for course:', course.title);
    console.log('Total modules:', course.modules.length);

    // Log course structure summary
    console.log('Course modules summary:');
    course.modules.forEach((module, index) => {
      console.log(`  Module ${index}: "${module.title}" (${module.lessons.length} lessons)`);
    });

    // Validate course structure
    if (!course.modules || course.modules.length === 0) {
      throw new Error('Course has no modules to export');
    }

    const zip = new JSZip();

    // Add manifest file (following reference structure)
    const manifest = this.generateManifest(course, options);
    zip.file('imsmanifest.xml', manifest);
    console.log('Generated manifest file');

    // Add single-page index.html (following reference structure)
    const indexHTML = this.generateIndexHTML(course);
    zip.file('index.html', indexHTML);
    console.log('Generated index.html file');

    // Add SCORM schema files (following reference structure)
    await this.addSCORMSchemaFiles(zip);
    console.log('Added SCORM schema files');

    // Add SCORM JavaScript files (following reference structure)
    await this.addSCORMJavaScriptFiles(zip);
    console.log('Added SCORM JavaScript files');

    // Count total lessons for verification
    const totalLessons = course.modules.reduce((total, module) => total + module.lessons.length, 0);
    console.log(`Total lessons in course: ${totalLessons}`);

    // List all files in the ZIP for verification
    console.log('\n=== ZIP PACKAGE CONTENTS ===');
    const zipFiles: string[] = [];
    zip.forEach((relativePath) => {
      zipFiles.push(relativePath);
    });

    // Sort files for better readability
    zipFiles.sort().forEach(filePath => {
      console.log(`  ${filePath}`);
    });

    console.log(`\n=== SINGLE-PAGE SCORM STRUCTURE ===`);
    console.log(`✅ All ${totalLessons} lessons included in single index.html file`);
    console.log(`✅ Following reference structure pattern`);
    console.log(`✅ SCORM 2004 4th Edition compliance`);

    // Generate and return the zip file
    console.log('Generating ZIP file...');
    const blob = await zip.generateAsync({ type: 'blob' });
    console.log(`SCORM export completed successfully. ZIP size: ${blob.size} bytes`);

    return blob;
  }
}

export const scormExporter = new SCORMExporter();

const validateCourseStructure = (course: Course): void => {
  console.log('=== DETAILED COURSE STRUCTURE VALIDATION ===');
  console.log('Course ID:', course.id);
  console.log('Course title:', course.title);
  console.log('Course description:', course.description);
  console.log('Number of modules:', course.modules?.length || 0);

  if (!course.modules || course.modules.length === 0) {
    throw new Error('Course has no modules to export');
  }

  console.log('\n=== MODULE BREAKDOWN ===');
  course.modules.forEach((module, moduleIndex) => {
    console.log(`Module ${moduleIndex}:`);
    console.log(`  ID: ${module.id}`);
    console.log(`  Title: ${module.title}`);
    console.log(`  Description: ${module.description}`);
    console.log(`  Lessons count: ${module.lessons?.length || 0}`);

    if (module.lessons && module.lessons.length > 0) {
      module.lessons.forEach((lesson, lessonIndex) => {
        console.log(`    Lesson ${lessonIndex}:`);
        console.log(`      ID: ${lesson.id}`);
        console.log(`      Title: ${lesson.title}`);
        console.log(`      Type: ${lesson.type}`);
        console.log(`      Duration: ${lesson.duration}`);
        console.log(`      Content blocks: ${lesson.contentBlocks?.length || 0}`);
        console.log(`      Video URL: ${lesson.metadata?.videoUrl || 'none'}`);
      });
    } else {
      console.warn(`    ⚠️ Module "${module.title}" has no lessons!`);
    }
  });

  // Count total lessons
  const totalLessons = course.modules.reduce((total, module) => {
    return total + (module.lessons?.length || 0);
  }, 0);
  console.log(`\n=== SUMMARY ===`);
  console.log(`Total modules: ${course.modules.length}`);
  console.log(`Total lessons: ${totalLessons}`);
  console.log(`Expected lesson files: lesson_0_0.html through lesson_${course.modules.length - 1}_1.html`);
};

export const downloadSCORMPackage = async (course: Course, options: SCORMExportOptions = {}) => {
  try {
    // Validate course structure before export
    validateCourseStructure(course);

    const blob = await scormExporter.exportCourse(course, options);
    
    // Create download link
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `${course.title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}_scorm.zip`;
    
    // Trigger download
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    // Clean up
    URL.revokeObjectURL(url);
    
    return true;
  } catch (error) {
    console.error('Error exporting SCORM package:', error);
    throw new Error('Failed to export SCORM package');
  }
};
