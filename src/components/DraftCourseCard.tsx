import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Edit,
  Trash2,
  Share,
  Download,
  MoreVertical,
  Clock,
  BookOpen,
  Play
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { toast } from "@/components/ui/sonner";
import { CourseStatusBadge } from "./CourseStatusBadge";
import { formatLastModified, courseStorage } from "@/services/courseStorage";
import { downloadSCORMPackage } from "@/services/scormExport";
import type { StoredCourse } from "@/services/courseStorage";

interface DraftCourseCardProps {
  course: StoredCourse;
  onEdit: (course: StoredCourse) => void;
  onDelete: (courseId: string) => void;
  onPublish: (courseId: string) => void;
}

export const DraftCourseCard = ({ course, onEdit, onDelete, onPublish }: DraftCourseCardProps) => {
  const [isDeleting, setIsDeleting] = useState(false);
  const [isPublishing, setIsPublishing] = useState(false);
  const [isExporting, setIsExporting] = useState(false);

  const handleDelete = async () => {
    if (!confirm("Are you sure you want to delete this course? This action cannot be undone.")) {
      return;
    }

    setIsDeleting(true);
    try {
      await courseStorage.deleteDraft(course.id);
      onDelete(course.id);
      toast.success("Course deleted successfully");
    } catch (error) {
      console.error('Error deleting course:', error);
      toast.error("Failed to delete course");
    } finally {
      setIsDeleting(false);
    }
  };

  const handlePublish = async () => {
    setIsPublishing(true);
    try {
      await courseStorage.publishCourse(course.id);
      onPublish(course.id);
      toast.success("Course published successfully");
    } catch (error) {
      console.error('Error publishing course:', error);
      toast.error("Failed to publish course");
    } finally {
      setIsPublishing(false);
    }
  };

  const handleExport = async () => {
    setIsExporting(true);
    try {
      await downloadSCORMPackage(course);
      toast.success("SCORM package downloaded successfully");
    } catch (error) {
      console.error('Error exporting SCORM:', error);
      toast.error("Failed to export SCORM package");
    } finally {
      setIsExporting(false);
    }
  };

  const moduleCount = course.modules?.length || 0;
  const lessonCount = course.modules?.reduce((total, module) => total + (module.lessons?.length || 0), 0) || 0;

  return (
    <Card className="hover:shadow-lg transition-all duration-300 cursor-pointer group h-56 flex flex-col">
      <CardHeader className="pb-2 flex-shrink-0">
        <div className="flex justify-between items-start mb-2">
          <CourseStatusBadge status={course.status} />
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <MoreVertical className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => onEdit(course)}>
                <Edit className="mr-2 h-4 w-4" />
                Edit Course
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleExport} disabled={isExporting}>
                <Download className="mr-2 h-4 w-4" />
                {isExporting ? "Exporting..." : "Download SCORM"}
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Share className="mr-2 h-4 w-4" />
                Share
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              {course.status === 'draft' && (
                <DropdownMenuItem onClick={handlePublish} disabled={isPublishing}>
                  <Play className="mr-2 h-4 w-4" />
                  {isPublishing ? "Publishing..." : "Publish"}
                </DropdownMenuItem>
              )}
              <DropdownMenuItem
                onClick={handleDelete}
                disabled={isDeleting}
                className="text-red-600 focus:text-red-600"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                {isDeleting ? "Deleting..." : "Delete"}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        <CardTitle
          className="text-base font-semibold group-hover:text-blue-600 transition-colors cursor-pointer line-clamp-2 mb-1"
          onClick={() => onEdit(course)}
        >
          {course.title || "Untitled Course"}
        </CardTitle>

        <CardDescription className="text-xs text-gray-500 line-clamp-2">
          {course.description || "No description available"}
        </CardDescription>
      </CardHeader>

      <CardContent className="pt-0 flex-1 flex flex-col">
        <div className="flex-1 flex flex-col justify-between">
          <div className="space-y-2">
            <div className="flex items-center justify-between text-xs text-gray-600">
              <div className="flex items-center space-x-3">
                <div className="flex items-center">
                  <BookOpen className="w-3 h-3 mr-1" />
                  <span>{moduleCount} module{moduleCount !== 1 ? 's' : ''}</span>
                </div>
                <div className="flex items-center">
                  <Play className="w-3 h-3 mr-1" />
                  <span>{lessonCount} lesson{lessonCount !== 1 ? 's' : ''}</span>
                </div>
              </div>
              <div className="flex items-center text-xs text-gray-400">
                <Clock className="w-3 h-3 mr-1" />
                <span>{formatLastModified(course.lastModified)}</span>
              </div>
            </div>
          </div>

          <div className="flex gap-2 pt-3 mt-auto">
            <Button
              size="sm"
              onClick={() => onEdit(course)}
              className="flex-1 bg-gradient-to-r from-blue-600 to-green-600 hover:from-blue-700 hover:to-green-700 text-xs h-8"
            >
              <Edit className="w-3 h-3 mr-1" />
              Continue Editing
            </Button>

            {course.status === 'draft' && (
              <Button
                size="sm"
                variant="outline"
                onClick={handlePublish}
                disabled={isPublishing}
                className="flex-1 text-xs h-8"
              >
                <Play className="w-3 h-3 mr-1" />
                {isPublishing ? "Publishing..." : "Publish"}
              </Button>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default DraftCourseCard;
